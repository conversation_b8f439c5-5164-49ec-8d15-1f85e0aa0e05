{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "go",
            "request": "launch",
            "name": "Launch Go Backend",
            "program": "${workspaceFolder}/backend/cmd/pentagi/main.go",
            "envFile": "${workspaceFolder}/.env",
            "env": {
                "CORS_ORIGINS": "http://localhost:*,https://localhost:*",
                "SERVER_PORT": "8080",
                "SERVER_USE_SSL": "false",
                "DATABASE_URL": "postgres://postgres:postgres@localhost:5432/pentagidb?sslmode=disable",
                "PUBLIC_URL": "http://localhost:8080",
                "STATIC_URL": "http://localhost:8000",
                // Choose it instead of STATIC_URL to serve static files from a directory:
                // "STATIC_DIR": "${workspaceFolder}/frontend/dist",
                // Langfuse (optional) uncomment to enable
                // "LANGFUSE_BASE_URL": "http://localhost:4000",
                // Observability (optional) uncomment to enable
                // "OTEL_HOST": "localhost:8148",
                // Scraper (optional) uncomment to enable
                "SCRAPER_PRIVATE_URL": "****************************************/",
                "LLM_SERVER_CONFIG_PATH": "${workspaceFolder}/examples/configs/openrouter.provider.yml",
            },
            "cwd": "${workspaceFolder}",
            "output": "${workspaceFolder}/build/__debug_bin_pentagi",
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Frontend",
            "runtimeExecutable": "npm",
            "runtimeArgs": ["run", "dev"],
            "env": {
                "VITE_APP_LOG_LEVEL": "DEBUG",
                "VITE_API_URL": "localhost:8080",
                "VITE_USE_HTTPS": "false",
                "VITE_PORT": "8000",
                "VITE_HOST": "0.0.0.0",
            },
            "cwd": "${workspaceFolder}/frontend",
        },
        {
            "type": "chrome",
            "request": "launch",
            "name": "Launch Browser",
            "url": "http://localhost:8000",
            "webRoot": "${workspaceFolder}/frontend/src",
        },
        {
            "type": "go",
            "request": "launch",
            "name": "Launch Agents Tests",
            "program": "${workspaceFolder}/backend/cmd/ctester/",
            "envFile": "${workspaceFolder}/.env",
            "env": {},
            "args": [
                // "-type", "openai",
                // "-type", "anthropic",
                "-config", "${workspaceFolder}/examples/configs/deepseek.provider.yml",
                // "-config", "${workspaceFolder}/examples/configs/openrouter.provider.yml",
                // "-config", "${workspaceFolder}/examples/configs/deepinfra.provider.yml",
                // "-report", "${workspaceFolder}/examples/tests/openai-report.md",
                // "-report", "${workspaceFolder}/examples/tests/anthropic-report.md",
                "-report", "${workspaceFolder}/examples/tests/deepseek-report.md",
                // "-report", "${workspaceFolder}/examples/tests/openrouter-report.md",
                // "-report", "${workspaceFolder}/examples/tests/deepinfra-report.md",
                "-agents", "all",
                "-tests", "all",
                "-verbose",
            ],
            "cwd": "${workspaceFolder}",
            "output": "${workspaceFolder}/build/__debug_bin_ctester",
        },
        {
            "type": "go",
            "request": "launch",
            "name": "Launch Tools Tests",
            "program": "${workspaceFolder}/backend/cmd/ftester/",
            "envFile": "${workspaceFolder}/.env",
            "env": {
                "LLM_SERVER_CONFIG_PATH": "${workspaceFolder}/examples/configs/openrouter.provider.yml",
                "DATABASE_URL": "postgres://postgres:postgres@localhost:5432/pentagidb?sslmode=disable",
                // Langfuse (optional) uncomment to enable
                "LANGFUSE_BASE_URL": "http://localhost:4000",
                // Observability (optional) uncomment to enable
                "OTEL_HOST": "localhost:8148",
            },
            "args": [
                "-flow", "0",
                // "describe", "-verbose",
                "search", "-question", "how I can install burp suite on Kali Linux?",
            ],
            "cwd": "${workspaceFolder}",
            "output": "${workspaceFolder}/build/__debug_bin_ftester",
        },
        {
            "type": "go",
            "request": "launch",
            "name": "Launch Embedding Tests",
            "program": "${workspaceFolder}/backend/cmd/etester/",
            "envFile": "${workspaceFolder}/.env",
            "env": {
                "DATABASE_URL": "postgres://postgres:postgres@localhost:5432/pentagidb?sslmode=disable",
            },
            "args": [
                "info", "-verbose",
            ],
            "cwd": "${workspaceFolder}",
            "output": "${workspaceFolder}/build/__debug_bin_etester",
        },
    ]
}