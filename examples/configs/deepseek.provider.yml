simple:
  model: "deepseek-chat"
  temperature: 0.6
  top_p: 0.95
  n: 1
  max_tokens: 4000
  price:
    input: 0.27
    output: 1.1

simple_json:
  model: "deepseek-chat"
  temperature: 0.7
  top_p: 1.0
  n: 1
  max_tokens: 4000
  json: true
  price:
    input: 0.27
    output: 1.1

agent:
  model: "deepseek-chat"
  temperature: 0.7
  top_p: 0.95
  n: 1
  max_tokens: 4000
  price:
    input: 0.27
    output: 1.1

assistant:
  model: "deepseek-chat"
  temperature: 0.6
  top_p: 0.9
  n: 1
  max_tokens: 6000
  price:
    input: 0.27
    output: 1.1

generator:
  model: "deepseek-chat"
  temperature: 1.1
  top_p: 0.95
  n: 1
  max_tokens: 8000
  price:
    input: 0.27
    output: 1.1

refiner:
  model: "deepseek-chat"
  temperature: 0.8
  top_p: 0.95
  n: 1
  max_tokens: 8000
  price:
    input: 0.27
    output: 1.1

adviser:
  model: "deepseek-chat"
  temperature: 1.2
  top_p: 0.95
  n: 1
  max_tokens: 4000
  price:
    input: 0.27
    output: 1.1

reflector:
  model: "deepseek-chat"
  temperature: 0.65
  top_p: 0.9
  n: 1
  max_tokens: 4000
  price:
    input: 0.27
    output: 1.1

searcher:
  model: "deepseek-chat"
  temperature: 1.0
  top_p: 0.95
  n: 1
  max_tokens: 4000
  price:
    input: 0.27
    output: 1.1

enricher:
  model: "deepseek-chat"
  temperature: 0.95
  top_p: 1.0
  n: 1
  max_tokens: 6000
  price:
    input: 0.27
    output: 1.1

coder:
  model: "deepseek-coder"
  temperature: 0.7
  top_p: 1.0
  n: 1
  max_tokens: 8000
  price:
    input: 0.27
    output: 1.1

installer:
  model: "deepseek-coder"
  temperature: 1.2
  top_p: 1.0
  n: 1
  max_tokens: 4000
  price:
    input: 0.27
    output: 1.1

pentester:
  model: "deepseek-chat"
  temperature: 0.8
  top_p: 0.9
  n: 1
  max_tokens: 4000
  price:
    input: 0.27
    output: 1.1
