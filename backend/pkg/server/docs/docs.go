// Package docs GENERATED BY SWAG; DO NOT EDIT
// This file was generated by swaggo/swag
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "PentAGI Development Team",
            "url": "https://pentagi.com",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "MIT",
            "url": "https://opensource.org/license/mit"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/agentlogs/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Agentlogs"
                ],
                "summary": "Retrieve agentlogs list",
                "parameters": [
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "agentlogs list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.agentlogs"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting agentlogs not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting agentlogs",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/assistantlogs/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Assistantlogs"
                ],
                "summary": "Retrieve assistantlogs list",
                "parameters": [
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "assistantlogs list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.assistantlogs"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting assistantlogs not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting assistantlogs",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/auth/authorize": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Public"
                ],
                "summary": "Login user into OAuth2 external system via HTTP redirect",
                "parameters": [
                    {
                        "type": "string",
                        "default": "/",
                        "description": "URI to redirect user there after login",
                        "name": "return_uri",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "default": "google",
                        "description": "OAuth provider name (google, github, etc.)",
                        "name": "provider",
                        "in": "query"
                    }
                ],
                "responses": {
                    "307": {
                        "description": "redirect to SSO login page"
                    },
                    "400": {
                        "description": "invalid autorizarion query",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "authorize not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on autorizarion",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/auth/login": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Public"
                ],
                "summary": "Login user into system",
                "parameters": [
                    {
                        "description": "Login form JSON data",
                        "name": "json",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.Login"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "login successful",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "400": {
                        "description": "invalid login data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "invalid login or password",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "login not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on login",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/auth/login-callback": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Public"
                ],
                "summary": "Login user from external OAuth application",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Auth code from OAuth provider to exchange token",
                        "name": "code",
                        "in": "query"
                    }
                ],
                "responses": {
                    "303": {
                        "description": "redirect to registered return_uri path in the state"
                    },
                    "400": {
                        "description": "invalid login data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "invalid login or password",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "login not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on login",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Public"
                ],
                "summary": "Login user from external OAuth application",
                "parameters": [
                    {
                        "description": "Auth form JSON data",
                        "name": "json",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.AuthCallback"
                        }
                    }
                ],
                "responses": {
                    "303": {
                        "description": "redirect to registered return_uri path in the state"
                    },
                    "400": {
                        "description": "invalid login data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "invalid login or password",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "login not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on login",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/auth/logout": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Public"
                ],
                "summary": "Logout current user via HTTP redirect",
                "parameters": [
                    {
                        "type": "string",
                        "default": "/",
                        "description": "URI to redirect user there after logout",
                        "name": "return_uri",
                        "in": "query"
                    }
                ],
                "responses": {
                    "307": {
                        "description": "redirect to input return_uri path"
                    }
                }
            }
        },
        "/auth/logout-callback": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "Public"
                ],
                "summary": "Logout current user from external OAuth application",
                "responses": {
                    "303": {
                        "description": "logout successful",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    }
                }
            }
        },
        "/containers/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Containers"
                ],
                "summary": "Retrieve containers list",
                "parameters": [
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "containers list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.containers"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting containers not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting containers",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Flows"
                ],
                "summary": "Retrieve flows list",
                "parameters": [
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "flows list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.flows"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting flows not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting flows",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Flows"
                ],
                "summary": "Create new flow with custom functions",
                "parameters": [
                    {
                        "description": "flow model to create",
                        "name": "json",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.CreateFlow"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "flow created successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.Flow"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid flow request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "creating flow not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on creating flow",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/{flowID}": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Flows"
                ],
                "summary": "Retrieve flow by id",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "flow received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.Flow"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "403": {
                        "description": "getting flow not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "flow not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting flow",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Flows"
                ],
                "summary": "Patch flow",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "flow model to patch",
                        "name": "json",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.PatchFlow"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "flow patched successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.Flow"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid flow request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "patching flow not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on patching flow",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "tags": [
                    "Flows"
                ],
                "summary": "Delete flow by id",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "flow deleted successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.Flow"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "403": {
                        "description": "deleting flow not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "flow not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on deleting flow",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/{flowID}/agentlogs/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Agentlogs"
                ],
                "summary": "Retrieve agentlogs list by flow id",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "agentlogs list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.agentlogs"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting agentlogs not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting agentlogs",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/{flowID}/assistantlogs/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Assistantlogs"
                ],
                "summary": "Retrieve assistantlogs list by flow id",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "assistantlogs list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.assistantlogs"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting assistantlogs not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting assistantlogs",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/{flowID}/assistants/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Assistants"
                ],
                "summary": "Retrieve assistants list",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "assistants list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.assistants"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting assistants not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting assistants",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Assistants"
                ],
                "summary": "Create new assistant with custom functions",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "assistant model to create",
                        "name": "json",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.CreateAssistant"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "assistant created successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.AssistantFlow"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid assistant request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "creating assistant not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on creating assistant",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/{flowID}/assistants/{assistantID}": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Assistants"
                ],
                "summary": "Retrieve flow assistant by id",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "assistant id",
                        "name": "assistantID",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "flow assistant received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.Assistant"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "403": {
                        "description": "getting flow assistant not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "flow assistant not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting flow assistant",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Assistants"
                ],
                "summary": "Patch assistant",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "assistant id",
                        "name": "assistantID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "assistant model to patch",
                        "name": "json",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.PatchAssistant"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "assistant patched successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.AssistantFlow"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid assistant request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "patching assistant not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on patching assistant",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "tags": [
                    "Assistants"
                ],
                "summary": "Delete assistant by id",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "assistant id",
                        "name": "assistantID",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "assistant deleted successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.AssistantFlow"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "403": {
                        "description": "deleting assistant not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "assistant not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on deleting assistant",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/{flowID}/containers/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Containers"
                ],
                "summary": "Retrieve containers list by flow id",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "containers list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.containers"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting containers not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting containers",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/{flowID}/containers/{containerID}": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Containers"
                ],
                "summary": "Retrieve container info by id and flow id",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "container id",
                        "name": "containerID",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "container info received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.Container"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "403": {
                        "description": "getting container not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "container not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting container",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/{flowID}/graph": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Flows"
                ],
                "summary": "Retrieve flow graph by id",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "flow graph received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.FlowTasksSubtasks"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "403": {
                        "description": "getting flow graph not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "flow graph not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting flow graph",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/{flowID}/msglogs/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Msglogs"
                ],
                "summary": "Retrieve msglogs list by flow id",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "msglogs list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.msglogs"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting msglogs not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting msglogs",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/{flowID}/screenshots/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Screenshots"
                ],
                "summary": "Retrieve screenshots list by flow id",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "screenshots list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.screenshots"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting screenshots not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting screenshots",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/{flowID}/screenshots/{screenshotID}": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Screenshots"
                ],
                "summary": "Retrieve screenshot info by id and flow id",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "screenshot id",
                        "name": "screenshotID",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "screenshot info received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.Screenshot"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "403": {
                        "description": "getting screenshot not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "screenshot not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting screenshot",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/{flowID}/screenshots/{screenshotID}/file": {
            "get": {
                "produces": [
                    "image/png",
                    "application/json"
                ],
                "tags": [
                    "Screenshots"
                ],
                "summary": "Retrieve screenshot file by id and flow id",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "screenshot id",
                        "name": "screenshotID",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "screenshot file",
                        "schema": {
                            "type": "file"
                        }
                    },
                    "403": {
                        "description": "getting screenshot not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting screenshot",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/{flowID}/searchlogs/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Searchlogs"
                ],
                "summary": "Retrieve searchlogs list by flow id",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "searchlogs list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.searchlogs"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting searchlogs not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting searchlogs",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/{flowID}/subtasks/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Subtasks"
                ],
                "summary": "Retrieve flow subtasks list",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "flow subtasks list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.subtasks"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting flow subtasks not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting flow subtasks",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/{flowID}/tasks/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Tasks"
                ],
                "summary": "Retrieve flow tasks list",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "flow tasks list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.tasks"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting flow tasks not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting flow tasks",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/{flowID}/tasks/{taskID}": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Tasks"
                ],
                "summary": "Retrieve flow task by id",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "task id",
                        "name": "taskID",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "flow task received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.Task"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "403": {
                        "description": "getting flow task not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "flow task not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting flow task",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/{flowID}/tasks/{taskID}/graph": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Tasks"
                ],
                "summary": "Retrieve flow task graph by id",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "task id",
                        "name": "taskID",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "flow task graph received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.FlowTasksSubtasks"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "403": {
                        "description": "getting flow task graph not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "flow task graph not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting flow task graph",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/{flowID}/tasks/{taskID}/subtasks/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Subtasks"
                ],
                "summary": "Retrieve flow task subtasks list",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "task id",
                        "name": "taskID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "flow task subtasks list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.subtasks"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting flow task subtasks not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting flow subtasks",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/{flowID}/tasks/{taskID}/subtasks/{subtaskID}": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Subtasks"
                ],
                "summary": "Retrieve flow task subtask by id",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "task id",
                        "name": "taskID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "subtask id",
                        "name": "subtaskID",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "flow task subtask received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.Subtask"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "403": {
                        "description": "getting flow task subtask not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "flow task subtask not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting flow task subtask",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/{flowID}/termlogs/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Termlogs"
                ],
                "summary": "Retrieve termlogs list by flow id",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "termlogs list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.termlogs"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting termlogs not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting termlogs",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/flows/{flowID}/vecstorelogs/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Vecstorelogs"
                ],
                "summary": "Retrieve vecstorelogs list by flow id",
                "parameters": [
                    {
                        "minimum": 0,
                        "type": "integer",
                        "description": "flow id",
                        "name": "flowID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "vecstorelogs list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.vecstorelogs"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting vecstorelogs not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting vecstorelogs",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/graphql": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "GraphQL"
                ],
                "summary": "Perform graphql requests",
                "parameters": [
                    {
                        "description": "graphql request",
                        "name": "json",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/graphql.RawParams"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "graphql response",
                        "schema": {
                            "$ref": "#/definitions/graphql.Response"
                        }
                    },
                    "400": {
                        "description": "invalid graphql request data",
                        "schema": {
                            "$ref": "#/definitions/graphql.Response"
                        }
                    },
                    "403": {
                        "description": "unauthorized",
                        "schema": {
                            "$ref": "#/definitions/graphql.Response"
                        }
                    },
                    "500": {
                        "description": "internal error on graphql request",
                        "schema": {
                            "$ref": "#/definitions/graphql.Response"
                        }
                    }
                }
            }
        },
        "/info": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Public"
                ],
                "summary": "Retrieve current user and system settings",
                "parameters": [
                    {
                        "type": "boolean",
                        "description": "boolean arg to refresh current cookie, use explicit false",
                        "name": "refresh_cookie",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "info received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.info"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "403": {
                        "description": "getting info not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "user not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting information about system and config",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/msglogs/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Msglogs"
                ],
                "summary": "Retrieve msglogs list",
                "parameters": [
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "msglogs list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.msglogs"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting msglogs not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting msglogs",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/prompts/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Prompts"
                ],
                "summary": "Retrieve prompts list",
                "parameters": [
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "prompts list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.prompts"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting prompts not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting prompts",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/prompts/{promptType}": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Prompts"
                ],
                "summary": "Retrieve prompt by type",
                "parameters": [
                    {
                        "type": "string",
                        "description": "prompt type",
                        "name": "promptType",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "prompt received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.Prompt"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid prompt request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting prompt not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "prompt not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting prompt",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Prompts"
                ],
                "summary": "Update prompt",
                "parameters": [
                    {
                        "type": "string",
                        "description": "prompt type",
                        "name": "promptType",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "prompt model to update",
                        "name": "json",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.PatchPrompt"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "prompt updated successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.Prompt"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid prompt request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "updating prompt not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "prompt not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on updating prompt",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/prompts/{promptType}/default": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Prompts"
                ],
                "summary": "Reset prompt by type to default value",
                "parameters": [
                    {
                        "type": "string",
                        "description": "prompt type",
                        "name": "promptType",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "prompt reset successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.Prompt"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid prompt request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "updating prompt not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "prompt not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on resetting prompt",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/providers/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Providers"
                ],
                "summary": "Retrieve providers list",
                "responses": {
                    "200": {
                        "description": "providers list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "type": "string"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "403": {
                        "description": "getting providers not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/roles/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Roles"
                ],
                "summary": "Retrieve roles list",
                "parameters": [
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "roles list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.roles"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting roles not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting roles",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/roles/{roleID}": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Roles"
                ],
                "summary": "Retrieve role by id",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "role id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "role received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.RolePrivileges"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting role not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting role",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/screenshots/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Screenshots"
                ],
                "summary": "Retrieve screenshots list",
                "parameters": [
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "screenshots list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.screenshots"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting screenshots not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting screenshots",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/searchlogs/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Searchlogs"
                ],
                "summary": "Retrieve searchlogs list",
                "parameters": [
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "searchlogs list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.searchlogs"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting searchlogs not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting searchlogs",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/termlogs/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Termlogs"
                ],
                "summary": "Retrieve termlogs list",
                "parameters": [
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "termlogs list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.termlogs"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting termlogs not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting termlogs",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/token": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Proto"
                ],
                "summary": "Create new JWT token to use it into automation connections",
                "parameters": [
                    {
                        "description": "Proto auth token request JSON data",
                        "name": "json",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.ProtoAuthTokenRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "token created successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.ProtoAuthToken"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid requested token info",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "creating token not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on creating token",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/user/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Retrieve current user information",
                "responses": {
                    "200": {
                        "description": "user info received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UserRolePrivileges"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "403": {
                        "description": "getting current user not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "current user not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting current user",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/user/password": {
            "put": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Update password for current user (account)",
                "parameters": [
                    {
                        "description": "container to validate and update account password",
                        "name": "json",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.Password"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "account password updated successful",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "400": {
                        "description": "invalid account password form data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "updating account password not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "current user not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on updating account password",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/users/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Retrieve users list by filters",
                "parameters": [
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "users list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.users"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting users not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting users",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Create new user",
                "parameters": [
                    {
                        "description": "user model to create from",
                        "name": "json",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.UserPassword"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "user created successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UserRole"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid user request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "creating user not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on creating user",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/users/{hash}": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Retrieve user by hash",
                "parameters": [
                    {
                        "maxLength": 32,
                        "minLength": 32,
                        "type": "string",
                        "description": "hash in hex format (md5)",
                        "name": "hash",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "user received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UserRolePrivileges"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "403": {
                        "description": "getting user not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "user not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting user",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Update user",
                "parameters": [
                    {
                        "maxLength": 32,
                        "minLength": 32,
                        "type": "string",
                        "description": "user hash in hex format (md5)",
                        "name": "hash",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "user model to update",
                        "name": "json",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.UserPassword"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "user updated successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UserRole"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid user request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "updating user not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "user not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on updating user",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Delete user by hash",
                "parameters": [
                    {
                        "maxLength": 32,
                        "minLength": 32,
                        "type": "string",
                        "description": "hash in hex format (md5)",
                        "name": "hash",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "user deleted successful",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "403": {
                        "description": "deleting user not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "user not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on deleting user",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/vecstorelogs/": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Vecstorelogs"
                ],
                "summary": "Retrieve vecstorelogs list",
                "parameters": [
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "multi",
                        "description": "Filtering result on server e.g. {\"value\":[...],\"field\":\"...\"}\n  field value should be integer or string or array type",
                        "name": "filters[]",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Field to group results by",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "minimum": 1,
                        "type": "integer",
                        "default": 1,
                        "description": "Number of page (since 1)",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "maximum": 1000,
                        "minimum": -1,
                        "type": "integer",
                        "default": 5,
                        "description": "Amount items per page (min -1, max 1000, -1 means unlimited)",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "{}",
                        "description": "Sorting result on server e.g. {\"prop\":\"...\",\"order\":\"...\"}\n  field order is \"ascending\" or \"descending\" value",
                        "name": "sort",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "sort",
                            "filter",
                            "init",
                            "page",
                            "size"
                        ],
                        "type": "string",
                        "default": "init",
                        "description": "Type of request",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "vecstorelogs list received successful",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.vecstorelogs"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "invalid query request data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "getting vecstorelogs not permitted",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "internal error on getting vecstorelogs",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "ErrorResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string",
                    "example": "Internal"
                },
                "error": {
                    "type": "string",
                    "example": "original server error message"
                },
                "msg": {
                    "type": "string",
                    "example": "internal server error"
                },
                "status": {
                    "type": "string",
                    "example": "error"
                }
            }
        },
        "SuccessResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "object"
                },
                "status": {
                    "type": "string",
                    "example": "success"
                }
            }
        },
        "gqlerror.Error": {
            "type": "object",
            "properties": {
                "extensions": {
                    "type": "object",
                    "additionalProperties": true
                },
                "locations": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gqlerror.Location"
                    }
                },
                "message": {
                    "type": "string"
                },
                "path": {
                    "type": "array",
                    "items": {}
                }
            }
        },
        "gqlerror.Location": {
            "type": "object",
            "properties": {
                "column": {
                    "type": "integer"
                },
                "line": {
                    "type": "integer"
                }
            }
        },
        "graphql.RawParams": {
            "type": "object",
            "properties": {
                "extensions": {
                    "type": "object",
                    "additionalProperties": true
                },
                "headers": {
                    "$ref": "#/definitions/http.Header"
                },
                "operationName": {
                    "type": "string"
                },
                "query": {
                    "type": "string"
                },
                "variables": {
                    "type": "object",
                    "additionalProperties": true
                }
            }
        },
        "graphql.Response": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "errors": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gqlerror.Error"
                    }
                },
                "extensions": {
                    "type": "object",
                    "additionalProperties": true
                },
                "hasNext": {
                    "type": "boolean"
                },
                "label": {
                    "type": "string"
                },
                "path": {
                    "type": "array",
                    "items": {}
                }
            }
        },
        "http.Header": {
            "type": "object",
            "additionalProperties": {
                "type": "array",
                "items": {
                    "type": "string"
                }
            }
        },
        "models.Agentlog": {
            "type": "object",
            "required": [
                "executor",
                "initiator",
                "task"
            ],
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "executor": {
                    "type": "string"
                },
                "flow_id": {
                    "type": "integer",
                    "minimum": 0
                },
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "initiator": {
                    "type": "string"
                },
                "result": {
                    "type": "string"
                },
                "subtask_id": {
                    "type": "integer"
                },
                "task": {
                    "type": "string"
                },
                "task_id": {
                    "type": "integer"
                }
            }
        },
        "models.Assistant": {
            "type": "object",
            "required": [
                "language",
                "model",
                "model_provider",
                "status",
                "title"
            ],
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "deleted_at": {
                    "type": "string"
                },
                "flow_id": {
                    "type": "integer",
                    "minimum": 0
                },
                "functions": {
                    "$ref": "#/definitions/tools.Functions"
                },
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "language": {
                    "type": "string",
                    "maxLength": 70
                },
                "model": {
                    "type": "string",
                    "maxLength": 70
                },
                "model_provider": {
                    "type": "string",
                    "maxLength": 70
                },
                "status": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "models.AssistantFlow": {
            "type": "object",
            "required": [
                "language",
                "model",
                "model_provider",
                "status",
                "title"
            ],
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "deleted_at": {
                    "type": "string"
                },
                "flow": {
                    "$ref": "#/definitions/models.Flow"
                },
                "flow_id": {
                    "type": "integer",
                    "minimum": 0
                },
                "functions": {
                    "$ref": "#/definitions/tools.Functions"
                },
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "language": {
                    "type": "string",
                    "maxLength": 70
                },
                "model": {
                    "type": "string",
                    "maxLength": 70
                },
                "model_provider": {
                    "type": "string",
                    "maxLength": 70
                },
                "status": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "models.Assistantlog": {
            "type": "object",
            "required": [
                "message",
                "result_format",
                "type"
            ],
            "properties": {
                "assistant_id": {
                    "type": "integer",
                    "minimum": 0
                },
                "created_at": {
                    "type": "string"
                },
                "flow_id": {
                    "type": "integer",
                    "minimum": 0
                },
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "message": {
                    "type": "string"
                },
                "result": {
                    "type": "string"
                },
                "result_format": {
                    "type": "string"
                },
                "thinking": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.AuthCallback": {
            "type": "object",
            "required": [
                "code",
                "id_token",
                "scope",
                "state"
            ],
            "properties": {
                "code": {
                    "type": "string"
                },
                "id_token": {
                    "type": "string"
                },
                "scope": {
                    "type": "string"
                },
                "state": {
                    "type": "string"
                }
            }
        },
        "models.Container": {
            "type": "object",
            "required": [
                "image",
                "local_dir",
                "local_id",
                "name",
                "status",
                "type"
            ],
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "flow_id": {
                    "type": "integer",
                    "minimum": 0
                },
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "image": {
                    "type": "string"
                },
                "local_dir": {
                    "type": "string"
                },
                "local_id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "models.CreateAssistant": {
            "type": "object",
            "required": [
                "input",
                "provider"
            ],
            "properties": {
                "functions": {
                    "$ref": "#/definitions/tools.Functions"
                },
                "input": {
                    "type": "string",
                    "example": "user input for running assistant"
                },
                "provider": {
                    "type": "string",
                    "example": "openai"
                },
                "use_agents": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "models.CreateFlow": {
            "type": "object",
            "required": [
                "input",
                "provider"
            ],
            "properties": {
                "functions": {
                    "$ref": "#/definitions/tools.Functions"
                },
                "input": {
                    "type": "string",
                    "example": "user input for first task in the flow"
                },
                "provider": {
                    "type": "string",
                    "example": "openai"
                }
            }
        },
        "models.Flow": {
            "type": "object",
            "required": [
                "language",
                "model",
                "model_provider",
                "status",
                "title"
            ],
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "deleted_at": {
                    "type": "string"
                },
                "functions": {
                    "$ref": "#/definitions/tools.Functions"
                },
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "language": {
                    "type": "string",
                    "maxLength": 70
                },
                "model": {
                    "type": "string",
                    "maxLength": 70
                },
                "model_provider": {
                    "type": "string",
                    "maxLength": 70
                },
                "status": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "user_id": {
                    "type": "integer",
                    "minimum": 0
                }
            }
        },
        "models.FlowTasksSubtasks": {
            "type": "object",
            "required": [
                "language",
                "model",
                "model_provider",
                "status",
                "tasks",
                "title"
            ],
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "deleted_at": {
                    "type": "string"
                },
                "functions": {
                    "$ref": "#/definitions/tools.Functions"
                },
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "language": {
                    "type": "string",
                    "maxLength": 70
                },
                "model": {
                    "type": "string",
                    "maxLength": 70
                },
                "model_provider": {
                    "type": "string",
                    "maxLength": 70
                },
                "status": {
                    "type": "string"
                },
                "tasks": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.TaskSubtasks"
                    }
                },
                "title": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "user_id": {
                    "type": "integer",
                    "minimum": 0
                }
            }
        },
        "models.Login": {
            "type": "object",
            "required": [
                "mail",
                "password"
            ],
            "properties": {
                "mail": {
                    "type": "string",
                    "maxLength": 50
                },
                "password": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 4
                }
            }
        },
        "models.Msglog": {
            "type": "object",
            "required": [
                "message",
                "result_format",
                "type"
            ],
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "flow_id": {
                    "type": "integer",
                    "minimum": 0
                },
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "message": {
                    "type": "string"
                },
                "result": {
                    "type": "string"
                },
                "result_format": {
                    "type": "string"
                },
                "subtask_id": {
                    "type": "integer"
                },
                "task_id": {
                    "type": "integer"
                },
                "thinking": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.Password": {
            "type": "object",
            "required": [
                "current_password",
                "password"
            ],
            "properties": {
                "confirm_password": {
                    "type": "string"
                },
                "current_password": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 5
                },
                "password": {
                    "type": "string",
                    "maxLength": 100
                }
            }
        },
        "models.PatchAssistant": {
            "type": "object",
            "required": [
                "action"
            ],
            "properties": {
                "action": {
                    "type": "string",
                    "default": "stop",
                    "enum": [
                        "stop",
                        "input"
                    ]
                },
                "input": {
                    "type": "string",
                    "example": "user input for waiting assistant"
                },
                "use_agents": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "models.PatchFlow": {
            "type": "object",
            "required": [
                "action"
            ],
            "properties": {
                "action": {
                    "type": "string",
                    "default": "stop",
                    "enum": [
                        "stop",
                        "finish",
                        "input"
                    ]
                },
                "input": {
                    "type": "string",
                    "example": "user input for waiting flow"
                }
            }
        },
        "models.PatchPrompt": {
            "type": "object",
            "required": [
                "prompt"
            ],
            "properties": {
                "prompt": {
                    "type": "string"
                }
            }
        },
        "models.Privilege": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "name": {
                    "type": "string",
                    "maxLength": 70
                },
                "role_id": {
                    "type": "integer",
                    "minimum": 0
                }
            }
        },
        "models.Prompt": {
            "type": "object",
            "required": [
                "prompt",
                "type"
            ],
            "properties": {
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "prompt": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "user_id": {
                    "type": "integer",
                    "minimum": 0
                }
            }
        },
        "models.ProtoAuthToken": {
            "type": "object",
            "required": [
                "token",
                "ttl"
            ],
            "properties": {
                "created_date": {
                    "type": "string"
                },
                "token": {
                    "type": "string"
                },
                "ttl": {
                    "type": "integer",
                    "maximum": 94608000,
                    "minimum": 1
                }
            }
        },
        "models.ProtoAuthTokenRequest": {
            "type": "object",
            "required": [
                "ttl",
                "type"
            ],
            "properties": {
                "ttl": {
                    "type": "integer",
                    "default": 31536000,
                    "maximum": 94608000,
                    "minimum": 1
                },
                "type": {
                    "type": "string",
                    "default": "automation",
                    "enum": [
                        "automation"
                    ]
                }
            }
        },
        "models.Role": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "name": {
                    "type": "string",
                    "maxLength": 50
                }
            }
        },
        "models.RolePrivileges": {
            "type": "object",
            "required": [
                "name",
                "privileges"
            ],
            "properties": {
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "name": {
                    "type": "string",
                    "maxLength": 50
                },
                "privileges": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Privilege"
                    }
                }
            }
        },
        "models.Screenshot": {
            "type": "object",
            "required": [
                "flow_id",
                "name",
                "url"
            ],
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "flow_id": {
                    "type": "integer",
                    "minimum": 0
                },
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "name": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "models.Searchlog": {
            "type": "object",
            "required": [
                "engine",
                "executor",
                "initiator",
                "query"
            ],
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "engine": {
                    "type": "string"
                },
                "executor": {
                    "type": "string"
                },
                "flow_id": {
                    "type": "integer",
                    "minimum": 0
                },
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "initiator": {
                    "type": "string"
                },
                "query": {
                    "type": "string"
                },
                "result": {
                    "type": "string"
                },
                "subtask_id": {
                    "type": "integer"
                },
                "task_id": {
                    "type": "integer"
                }
            }
        },
        "models.Subtask": {
            "type": "object",
            "required": [
                "description",
                "status",
                "title"
            ],
            "properties": {
                "context": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "result": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "task_id": {
                    "type": "integer",
                    "minimum": 0
                },
                "title": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "models.Task": {
            "type": "object",
            "required": [
                "input",
                "status",
                "title"
            ],
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "flow_id": {
                    "type": "integer",
                    "minimum": 0
                },
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "input": {
                    "type": "string"
                },
                "result": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "models.TaskSubtasks": {
            "type": "object",
            "required": [
                "input",
                "status",
                "subtasks",
                "title"
            ],
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "flow_id": {
                    "type": "integer",
                    "minimum": 0
                },
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "input": {
                    "type": "string"
                },
                "result": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "subtasks": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Subtask"
                    }
                },
                "title": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "models.Termlog": {
            "type": "object",
            "required": [
                "container_id",
                "text",
                "type"
            ],
            "properties": {
                "container_id": {
                    "type": "integer",
                    "minimum": 0
                },
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "text": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.User": {
            "type": "object",
            "required": [
                "created_at",
                "mail",
                "status",
                "type"
            ],
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "hash": {
                    "type": "string"
                },
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "mail": {
                    "type": "string",
                    "maxLength": 50
                },
                "name": {
                    "type": "string",
                    "maxLength": 70
                },
                "password_change_required": {
                    "type": "boolean"
                },
                "provider": {
                    "type": "string"
                },
                "role_id": {
                    "type": "integer",
                    "minimum": 0
                },
                "status": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.UserPassword": {
            "type": "object",
            "required": [
                "created_at",
                "mail",
                "password",
                "status",
                "type"
            ],
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "hash": {
                    "type": "string"
                },
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "mail": {
                    "type": "string",
                    "maxLength": 50
                },
                "name": {
                    "type": "string",
                    "maxLength": 70
                },
                "password": {
                    "type": "string",
                    "maxLength": 100
                },
                "password_change_required": {
                    "type": "boolean"
                },
                "provider": {
                    "type": "string"
                },
                "role_id": {
                    "type": "integer",
                    "minimum": 0
                },
                "status": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.UserRole": {
            "type": "object",
            "required": [
                "created_at",
                "mail",
                "status",
                "type"
            ],
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "hash": {
                    "type": "string"
                },
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "mail": {
                    "type": "string",
                    "maxLength": 50
                },
                "name": {
                    "type": "string",
                    "maxLength": 70
                },
                "password_change_required": {
                    "type": "boolean"
                },
                "provider": {
                    "type": "string"
                },
                "role": {
                    "$ref": "#/definitions/models.Role"
                },
                "role_id": {
                    "type": "integer",
                    "minimum": 0
                },
                "status": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.UserRolePrivileges": {
            "type": "object",
            "required": [
                "created_at",
                "mail",
                "status",
                "type"
            ],
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "hash": {
                    "type": "string"
                },
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "mail": {
                    "type": "string",
                    "maxLength": 50
                },
                "name": {
                    "type": "string",
                    "maxLength": 70
                },
                "password_change_required": {
                    "type": "boolean"
                },
                "provider": {
                    "type": "string"
                },
                "role": {
                    "$ref": "#/definitions/models.RolePrivileges"
                },
                "role_id": {
                    "type": "integer",
                    "minimum": 0
                },
                "status": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.Vecstorelog": {
            "type": "object",
            "required": [
                "action",
                "executor",
                "filter",
                "initiator",
                "query",
                "result"
            ],
            "properties": {
                "action": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "executor": {
                    "type": "string"
                },
                "filter": {
                    "type": "string"
                },
                "flow_id": {
                    "type": "integer",
                    "minimum": 0
                },
                "id": {
                    "type": "integer",
                    "minimum": 0
                },
                "initiator": {
                    "type": "string"
                },
                "query": {
                    "type": "string"
                },
                "result": {
                    "type": "string"
                },
                "subtask_id": {
                    "type": "integer"
                },
                "task_id": {
                    "type": "integer"
                }
            }
        },
        "services.agentlogs": {
            "type": "object",
            "properties": {
                "agentlogs": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Agentlog"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "services.assistantlogs": {
            "type": "object",
            "properties": {
                "assistantlogs": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Assistantlog"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "services.assistants": {
            "type": "object",
            "properties": {
                "assistants": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Assistant"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "services.containers": {
            "type": "object",
            "properties": {
                "containers": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Container"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "services.flows": {
            "type": "object",
            "properties": {
                "flows": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Flow"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "services.info": {
            "type": "object",
            "properties": {
                "develop": {
                    "type": "boolean"
                },
                "expires_at": {
                    "type": "string"
                },
                "issued_at": {
                    "type": "string"
                },
                "oauth": {
                    "type": "boolean"
                },
                "privileges": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "providers": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "role": {
                    "$ref": "#/definitions/models.Role"
                },
                "type": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/models.User"
                }
            }
        },
        "services.msglogs": {
            "type": "object",
            "properties": {
                "msglogs": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Msglog"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "services.prompts": {
            "type": "object",
            "properties": {
                "prompts": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Prompt"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "services.roles": {
            "type": "object",
            "properties": {
                "roles": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.RolePrivileges"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "services.screenshots": {
            "type": "object",
            "properties": {
                "screenshots": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Screenshot"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "services.searchlogs": {
            "type": "object",
            "properties": {
                "searchlogs": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Searchlog"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "services.subtasks": {
            "type": "object",
            "properties": {
                "subtasks": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Subtask"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "services.tasks": {
            "type": "object",
            "properties": {
                "tasks": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Task"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "services.termlogs": {
            "type": "object",
            "properties": {
                "termlogs": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Termlog"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "services.users": {
            "type": "object",
            "properties": {
                "total": {
                    "type": "integer"
                },
                "users": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.UserRole"
                    }
                }
            }
        },
        "services.vecstorelogs": {
            "type": "object",
            "properties": {
                "total": {
                    "type": "integer"
                },
                "vecstorelogs": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Vecstorelog"
                    }
                }
            }
        },
        "tools.DisableFunction": {
            "type": "object",
            "required": [
                "context",
                "name"
            ],
            "properties": {
                "context": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "tools.ExternalFunction": {
            "type": "object",
            "required": [
                "context",
                "name",
                "schema",
                "url"
            ],
            "properties": {
                "context": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "name": {
                    "type": "string"
                },
                "schema": {
                    "type": "object"
                },
                "timeout": {
                    "type": "integer",
                    "minimum": 1,
                    "example": 60
                },
                "url": {
                    "type": "string",
                    "example": "https://example.com/api/v1/function"
                }
            }
        },
        "tools.Functions": {
            "type": "object",
            "properties": {
                "disabled": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/tools.DisableFunction"
                    }
                },
                "functions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/tools.ExternalFunction"
                    }
                },
                "token": {
                    "type": "string"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "",
	BasePath:         "/api/v1",
	Schemes:          []string{},
	Title:            "PentAGI Swagger API",
	Description:      "Swagger API for Penetration Testing Advanced General Intelligence PentAGI.",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
