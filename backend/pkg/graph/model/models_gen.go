// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package model

import (
	"fmt"
	"io"
	"strconv"
	"time"
)

type AgentLog struct {
	ID        int64     `json:"id"`
	Initiator AgentType `json:"initiator"`
	Executor  AgentType `json:"executor"`
	Task      string    `json:"task"`
	Result    string    `json:"result"`
	FlowID    int64     `json:"flowId"`
	TaskID    *int64    `json:"taskId,omitempty"`
	SubtaskID *int64    `json:"subtaskId,omitempty"`
	CreatedAt time.Time `json:"createdAt"`
}

type Assistant struct {
	ID        int64      `json:"id"`
	Title     string     `json:"title"`
	Status    StatusType `json:"status"`
	Provider  string     `json:"provider"`
	FlowID    int64      `json:"flowId"`
	UseAgents bool       `json:"useAgents"`
	CreatedAt time.Time  `json:"createdAt"`
	UpdatedAt time.Time  `json:"updatedAt"`
}

type AssistantLog struct {
	ID           int64          `json:"id"`
	Type         MessageLogType `json:"type"`
	Message      string         `json:"message"`
	Thinking     *string        `json:"thinking,omitempty"`
	Result       string         `json:"result"`
	ResultFormat ResultFormat   `json:"resultFormat"`
	AppendPart   bool           `json:"appendPart"`
	FlowID       int64          `json:"flowId"`
	AssistantID  int64          `json:"assistantId"`
	CreatedAt    time.Time      `json:"createdAt"`
}

type Flow struct {
	ID        int64       `json:"id"`
	Title     string      `json:"title"`
	Status    StatusType  `json:"status"`
	Terminals []*Terminal `json:"terminals,omitempty"`
	Provider  string      `json:"provider"`
	CreatedAt time.Time   `json:"createdAt"`
	UpdatedAt time.Time   `json:"updatedAt"`
}

type FlowAssistant struct {
	Flow      *Flow      `json:"flow"`
	Assistant *Assistant `json:"assistant"`
}

type MessageLog struct {
	ID           int64          `json:"id"`
	Type         MessageLogType `json:"type"`
	Message      string         `json:"message"`
	Thinking     *string        `json:"thinking,omitempty"`
	Result       string         `json:"result"`
	ResultFormat ResultFormat   `json:"resultFormat"`
	FlowID       int64          `json:"flowId"`
	TaskID       *int64         `json:"taskId,omitempty"`
	SubtaskID    *int64         `json:"subtaskId,omitempty"`
	CreatedAt    time.Time      `json:"createdAt"`
}

type Mutation struct {
}

type Prompt struct {
	Type   string `json:"type"`
	Prompt string `json:"prompt"`
}

type Query struct {
}

type Screenshot struct {
	ID        int64     `json:"id"`
	FlowID    int64     `json:"flowId"`
	Name      string    `json:"name"`
	URL       string    `json:"url"`
	CreatedAt time.Time `json:"createdAt"`
}

type SearchLog struct {
	ID        int64     `json:"id"`
	Initiator AgentType `json:"initiator"`
	Executor  AgentType `json:"executor"`
	Engine    string    `json:"engine"`
	Query     string    `json:"query"`
	Result    string    `json:"result"`
	FlowID    int64     `json:"flowId"`
	TaskID    *int64    `json:"taskId,omitempty"`
	SubtaskID *int64    `json:"subtaskId,omitempty"`
	CreatedAt time.Time `json:"createdAt"`
}

type Settings struct {
	Debug              bool `json:"debug"`
	AskUser            bool `json:"askUser"`
	DockerInside       bool `json:"dockerInside"`
	AssistantUseAgents bool `json:"assistantUseAgents"`
}

type Subscription struct {
}

type Subtask struct {
	ID          int64      `json:"id"`
	Status      StatusType `json:"status"`
	Title       string     `json:"title"`
	Description string     `json:"description"`
	Result      string     `json:"result"`
	TaskID      int64      `json:"taskId"`
	CreatedAt   time.Time  `json:"createdAt"`
	UpdatedAt   time.Time  `json:"updatedAt"`
}

type Task struct {
	ID        int64      `json:"id"`
	Title     string     `json:"title"`
	Status    StatusType `json:"status"`
	Input     string     `json:"input"`
	Result    string     `json:"result"`
	FlowID    int64      `json:"flowId"`
	Subtasks  []*Subtask `json:"subtasks,omitempty"`
	CreatedAt time.Time  `json:"createdAt"`
	UpdatedAt time.Time  `json:"updatedAt"`
}

type Terminal struct {
	ID        int64        `json:"id"`
	Type      TerminalType `json:"type"`
	Name      string       `json:"name"`
	Image     string       `json:"image"`
	Connected bool         `json:"connected"`
	CreatedAt time.Time    `json:"createdAt"`
}

type TerminalLog struct {
	ID        int64           `json:"id"`
	FlowID    int64           `json:"flowId"`
	Type      TerminalLogType `json:"type"`
	Text      string          `json:"text"`
	Terminal  int64           `json:"terminal"`
	CreatedAt time.Time       `json:"createdAt"`
}

type VectorStoreLog struct {
	ID        int64             `json:"id"`
	Initiator AgentType         `json:"initiator"`
	Executor  AgentType         `json:"executor"`
	Filter    string            `json:"filter"`
	Query     string            `json:"query"`
	Action    VectorStoreAction `json:"action"`
	Result    string            `json:"result"`
	FlowID    int64             `json:"flowId"`
	TaskID    *int64            `json:"taskId,omitempty"`
	SubtaskID *int64            `json:"subtaskId,omitempty"`
	CreatedAt time.Time         `json:"createdAt"`
}

type AgentType string

const (
	AgentTypePrimaryAgent  AgentType = "primary_agent"
	AgentTypeReporter      AgentType = "reporter"
	AgentTypeGenerator     AgentType = "generator"
	AgentTypeRefiner       AgentType = "refiner"
	AgentTypeReflector     AgentType = "reflector"
	AgentTypeEnricher      AgentType = "enricher"
	AgentTypeAdviser       AgentType = "adviser"
	AgentTypeCoder         AgentType = "coder"
	AgentTypeMemorist      AgentType = "memorist"
	AgentTypeSearcher      AgentType = "searcher"
	AgentTypeInstaller     AgentType = "installer"
	AgentTypePentester     AgentType = "pentester"
	AgentTypeSummarizer    AgentType = "summarizer"
	AgentTypeToolCallFixer AgentType = "tool_call_fixer"
	AgentTypeAssistant     AgentType = "assistant"
)

var AllAgentType = []AgentType{
	AgentTypePrimaryAgent,
	AgentTypeReporter,
	AgentTypeGenerator,
	AgentTypeRefiner,
	AgentTypeReflector,
	AgentTypeEnricher,
	AgentTypeAdviser,
	AgentTypeCoder,
	AgentTypeMemorist,
	AgentTypeSearcher,
	AgentTypeInstaller,
	AgentTypePentester,
	AgentTypeSummarizer,
	AgentTypeToolCallFixer,
	AgentTypeAssistant,
}

func (e AgentType) IsValid() bool {
	switch e {
	case AgentTypePrimaryAgent, AgentTypeReporter, AgentTypeGenerator, AgentTypeRefiner, AgentTypeReflector, AgentTypeEnricher, AgentTypeAdviser, AgentTypeCoder, AgentTypeMemorist, AgentTypeSearcher, AgentTypeInstaller, AgentTypePentester, AgentTypeSummarizer, AgentTypeToolCallFixer, AgentTypeAssistant:
		return true
	}
	return false
}

func (e AgentType) String() string {
	return string(e)
}

func (e *AgentType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = AgentType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid AgentType", str)
	}
	return nil
}

func (e AgentType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type MessageLogType string

const (
	MessageLogTypeAnswer   MessageLogType = "answer"
	MessageLogTypeReport   MessageLogType = "report"
	MessageLogTypeThoughts MessageLogType = "thoughts"
	MessageLogTypeBrowser  MessageLogType = "browser"
	MessageLogTypeTerminal MessageLogType = "terminal"
	MessageLogTypeFile     MessageLogType = "file"
	MessageLogTypeSearch   MessageLogType = "search"
	MessageLogTypeAdvice   MessageLogType = "advice"
	MessageLogTypeAsk      MessageLogType = "ask"
	MessageLogTypeInput    MessageLogType = "input"
	MessageLogTypeDone     MessageLogType = "done"
)

var AllMessageLogType = []MessageLogType{
	MessageLogTypeAnswer,
	MessageLogTypeReport,
	MessageLogTypeThoughts,
	MessageLogTypeBrowser,
	MessageLogTypeTerminal,
	MessageLogTypeFile,
	MessageLogTypeSearch,
	MessageLogTypeAdvice,
	MessageLogTypeAsk,
	MessageLogTypeInput,
	MessageLogTypeDone,
}

func (e MessageLogType) IsValid() bool {
	switch e {
	case MessageLogTypeAnswer, MessageLogTypeReport, MessageLogTypeThoughts, MessageLogTypeBrowser, MessageLogTypeTerminal, MessageLogTypeFile, MessageLogTypeSearch, MessageLogTypeAdvice, MessageLogTypeAsk, MessageLogTypeInput, MessageLogTypeDone:
		return true
	}
	return false
}

func (e MessageLogType) String() string {
	return string(e)
}

func (e *MessageLogType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = MessageLogType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid MessageLogType", str)
	}
	return nil
}

func (e MessageLogType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type ResultFormat string

const (
	ResultFormatPlain    ResultFormat = "plain"
	ResultFormatMarkdown ResultFormat = "markdown"
	ResultFormatTerminal ResultFormat = "terminal"
)

var AllResultFormat = []ResultFormat{
	ResultFormatPlain,
	ResultFormatMarkdown,
	ResultFormatTerminal,
}

func (e ResultFormat) IsValid() bool {
	switch e {
	case ResultFormatPlain, ResultFormatMarkdown, ResultFormatTerminal:
		return true
	}
	return false
}

func (e ResultFormat) String() string {
	return string(e)
}

func (e *ResultFormat) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = ResultFormat(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid ResultFormat", str)
	}
	return nil
}

func (e ResultFormat) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type ResultType string

const (
	ResultTypeSuccess ResultType = "success"
	ResultTypeError   ResultType = "error"
)

var AllResultType = []ResultType{
	ResultTypeSuccess,
	ResultTypeError,
}

func (e ResultType) IsValid() bool {
	switch e {
	case ResultTypeSuccess, ResultTypeError:
		return true
	}
	return false
}

func (e ResultType) String() string {
	return string(e)
}

func (e *ResultType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = ResultType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid ResultType", str)
	}
	return nil
}

func (e ResultType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type StatusType string

const (
	StatusTypeCreated  StatusType = "created"
	StatusTypeRunning  StatusType = "running"
	StatusTypeWaiting  StatusType = "waiting"
	StatusTypeFinished StatusType = "finished"
	StatusTypeFailed   StatusType = "failed"
)

var AllStatusType = []StatusType{
	StatusTypeCreated,
	StatusTypeRunning,
	StatusTypeWaiting,
	StatusTypeFinished,
	StatusTypeFailed,
}

func (e StatusType) IsValid() bool {
	switch e {
	case StatusTypeCreated, StatusTypeRunning, StatusTypeWaiting, StatusTypeFinished, StatusTypeFailed:
		return true
	}
	return false
}

func (e StatusType) String() string {
	return string(e)
}

func (e *StatusType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = StatusType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid StatusType", str)
	}
	return nil
}

func (e StatusType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type TerminalLogType string

const (
	TerminalLogTypeStdin  TerminalLogType = "stdin"
	TerminalLogTypeStdout TerminalLogType = "stdout"
	TerminalLogTypeStderr TerminalLogType = "stderr"
)

var AllTerminalLogType = []TerminalLogType{
	TerminalLogTypeStdin,
	TerminalLogTypeStdout,
	TerminalLogTypeStderr,
}

func (e TerminalLogType) IsValid() bool {
	switch e {
	case TerminalLogTypeStdin, TerminalLogTypeStdout, TerminalLogTypeStderr:
		return true
	}
	return false
}

func (e TerminalLogType) String() string {
	return string(e)
}

func (e *TerminalLogType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TerminalLogType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TerminalLogType", str)
	}
	return nil
}

func (e TerminalLogType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type TerminalType string

const (
	TerminalTypePrimary   TerminalType = "primary"
	TerminalTypeSecondary TerminalType = "secondary"
)

var AllTerminalType = []TerminalType{
	TerminalTypePrimary,
	TerminalTypeSecondary,
}

func (e TerminalType) IsValid() bool {
	switch e {
	case TerminalTypePrimary, TerminalTypeSecondary:
		return true
	}
	return false
}

func (e TerminalType) String() string {
	return string(e)
}

func (e *TerminalType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TerminalType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TerminalType", str)
	}
	return nil
}

func (e TerminalType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type VectorStoreAction string

const (
	VectorStoreActionRetrieve VectorStoreAction = "retrieve"
	VectorStoreActionStore    VectorStoreAction = "store"
)

var AllVectorStoreAction = []VectorStoreAction{
	VectorStoreActionRetrieve,
	VectorStoreActionStore,
}

func (e VectorStoreAction) IsValid() bool {
	switch e {
	case VectorStoreActionRetrieve, VectorStoreActionStore:
		return true
	}
	return false
}

func (e VectorStoreAction) String() string {
	return string(e)
}

func (e *VectorStoreAction) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = VectorStoreAction(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid VectorStoreAction", str)
	}
	return nil
}

func (e VectorStoreAction) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}
