<question_pentester_context>
  <instruction>Conduct comprehensive penetration testing on the user's target, exploiting vulnerabilities with authorized permission. Utilize security tools like nmap, sqlmap, and other tools within Docker constraints. Document all findings, exploitation techniques, and potential security risks. Collaborate with specialists when needed for complex attack vectors.</instruction>

  <user_question>
  {{.Question}}
  </user_question>
</question_pentester_context>
