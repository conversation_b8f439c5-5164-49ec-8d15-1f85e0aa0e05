<refinement_context>
  <instruction>Optimize the remaining subtasks for user's task based on execution results</instruction>
  
  <user_task>
    <input>{{.Task.Input}}</input>
  </user_task>
  
  {{if .Tasks}}
  <previous_tasks>
    {{range .Tasks}}
    <task>
      <id>{{.ID}}</id>
      <input>{{.Input}}</input>
      <status>{{.Status}}</status>
      <result>{{.Result}}</result>
    </task>
    {{end}}
  </previous_tasks>
  {{end}}
  
  {{if .CompletedSubtasks}}
  <completed_subtasks>
    {{range .CompletedSubtasks}}
    <subtask>
      <id>{{.ID}}</id>
      <title>{{.Title}}</title>
      <description>{{.Description}}</description>
      <status>{{.Status}}</status>
      <result>{{.Result}}</result>
    </subtask>
    {{end}}
  </completed_subtasks>
  {{end}}
  
  {{if .PlannedSubtasks}}
  <planned_subtasks>
    {{range .PlannedSubtasks}}
    <subtask>
      <id>{{.ID}}</id>
      <title>{{.Title}}</title>
      <description>{{.Description}}</description>
    </subtask>
    {{end}}
  </planned_subtasks>
  {{else}}
  <planned_subtasks>
    <status>empty</status>
    <message>All subtasks have been completed. Review their statuses and results.</message>
  </planned_subtasks>
  {{end}}

  {{if .ExecutionState}}
  <execution_state>
  {{.ExecutionState}}
  </execution_state>
  {{end}}
  
  {{if .ExecutionLogs}}
  <execution_logs>
  {{.ExecutionLogs}}
  </execution_logs>
  {{end}}
</refinement_context>
