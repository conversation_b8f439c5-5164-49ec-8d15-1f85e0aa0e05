// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0
// source: flows.sql

package database

import (
	"context"
	"database/sql"
	"encoding/json"
)

const createFlow = `-- name: CreateFlow :one
INSERT INTO flows (
  title, status, model, model_provider, language, functions, prompts, user_id
)
VALUES (
  $1, $2, $3, $4, $5, $6, $7, $8
)
RETURNING id, status, title, model, model_provider, language, functions, prompts, user_id, created_at, updated_at, deleted_at, trace_id
`

type CreateFlowParams struct {
	Title         string          `json:"title"`
	Status        FlowStatus      `json:"status"`
	Model         string          `json:"model"`
	ModelProvider string          `json:"model_provider"`
	Language      string          `json:"language"`
	Functions     json.RawMessage `json:"functions"`
	Prompts       json.RawMessage `json:"prompts"`
	UserID        int64           `json:"user_id"`
}

func (q *Queries) CreateFlow(ctx context.Context, arg CreateFlowParams) (Flow, error) {
	row := q.db.QueryRowContext(ctx, createFlow,
		arg.Title,
		arg.Status,
		arg.Model,
		arg.ModelProvider,
		arg.Language,
		arg.Functions,
		arg.Prompts,
		arg.UserID,
	)
	var i Flow
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Model,
		&i.ModelProvider,
		&i.Language,
		&i.Functions,
		&i.Prompts,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.TraceID,
	)
	return i, err
}

const deleteFlow = `-- name: DeleteFlow :one
UPDATE flows
SET deleted_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, status, title, model, model_provider, language, functions, prompts, user_id, created_at, updated_at, deleted_at, trace_id
`

func (q *Queries) DeleteFlow(ctx context.Context, id int64) (Flow, error) {
	row := q.db.QueryRowContext(ctx, deleteFlow, id)
	var i Flow
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Model,
		&i.ModelProvider,
		&i.Language,
		&i.Functions,
		&i.Prompts,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.TraceID,
	)
	return i, err
}

const getFlow = `-- name: GetFlow :one
SELECT
  f.id, f.status, f.title, f.model, f.model_provider, f.language, f.functions, f.prompts, f.user_id, f.created_at, f.updated_at, f.deleted_at, f.trace_id
FROM flows f
WHERE f.id = $1 AND f.deleted_at IS NULL
`

func (q *Queries) GetFlow(ctx context.Context, id int64) (Flow, error) {
	row := q.db.QueryRowContext(ctx, getFlow, id)
	var i Flow
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Model,
		&i.ModelProvider,
		&i.Language,
		&i.Functions,
		&i.Prompts,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.TraceID,
	)
	return i, err
}

const getFlows = `-- name: GetFlows :many
SELECT
  f.id, f.status, f.title, f.model, f.model_provider, f.language, f.functions, f.prompts, f.user_id, f.created_at, f.updated_at, f.deleted_at, f.trace_id
FROM flows f
WHERE f.deleted_at IS NULL
ORDER BY f.created_at DESC
`

func (q *Queries) GetFlows(ctx context.Context) ([]Flow, error) {
	rows, err := q.db.QueryContext(ctx, getFlows)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Flow
	for rows.Next() {
		var i Flow
		if err := rows.Scan(
			&i.ID,
			&i.Status,
			&i.Title,
			&i.Model,
			&i.ModelProvider,
			&i.Language,
			&i.Functions,
			&i.Prompts,
			&i.UserID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.TraceID,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserFlow = `-- name: GetUserFlow :one
SELECT
  f.id, f.status, f.title, f.model, f.model_provider, f.language, f.functions, f.prompts, f.user_id, f.created_at, f.updated_at, f.deleted_at, f.trace_id
FROM flows f
INNER JOIN users u ON f.user_id = u.id
WHERE f.id = $1 AND f.user_id = $2 AND f.deleted_at IS NULL
`

type GetUserFlowParams struct {
	ID     int64 `json:"id"`
	UserID int64 `json:"user_id"`
}

func (q *Queries) GetUserFlow(ctx context.Context, arg GetUserFlowParams) (Flow, error) {
	row := q.db.QueryRowContext(ctx, getUserFlow, arg.ID, arg.UserID)
	var i Flow
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Model,
		&i.ModelProvider,
		&i.Language,
		&i.Functions,
		&i.Prompts,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.TraceID,
	)
	return i, err
}

const getUserFlows = `-- name: GetUserFlows :many
SELECT
  f.id, f.status, f.title, f.model, f.model_provider, f.language, f.functions, f.prompts, f.user_id, f.created_at, f.updated_at, f.deleted_at, f.trace_id
FROM flows f
INNER JOIN users u ON f.user_id = u.id
WHERE f.user_id = $1 AND f.deleted_at IS NULL
ORDER BY f.created_at DESC
`

func (q *Queries) GetUserFlows(ctx context.Context, userID int64) ([]Flow, error) {
	rows, err := q.db.QueryContext(ctx, getUserFlows, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Flow
	for rows.Next() {
		var i Flow
		if err := rows.Scan(
			&i.ID,
			&i.Status,
			&i.Title,
			&i.Model,
			&i.ModelProvider,
			&i.Language,
			&i.Functions,
			&i.Prompts,
			&i.UserID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.TraceID,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateFlow = `-- name: UpdateFlow :one
UPDATE flows
SET title = $1, model = $2, language = $3, functions = $4, prompts = $5, trace_id = $6
WHERE id = $7
RETURNING id, status, title, model, model_provider, language, functions, prompts, user_id, created_at, updated_at, deleted_at, trace_id
`

type UpdateFlowParams struct {
	Title     string          `json:"title"`
	Model     string          `json:"model"`
	Language  string          `json:"language"`
	Functions json.RawMessage `json:"functions"`
	Prompts   json.RawMessage `json:"prompts"`
	TraceID   sql.NullString  `json:"trace_id"`
	ID        int64           `json:"id"`
}

func (q *Queries) UpdateFlow(ctx context.Context, arg UpdateFlowParams) (Flow, error) {
	row := q.db.QueryRowContext(ctx, updateFlow,
		arg.Title,
		arg.Model,
		arg.Language,
		arg.Functions,
		arg.Prompts,
		arg.TraceID,
		arg.ID,
	)
	var i Flow
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Model,
		&i.ModelProvider,
		&i.Language,
		&i.Functions,
		&i.Prompts,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.TraceID,
	)
	return i, err
}

const updateFlowLanguage = `-- name: UpdateFlowLanguage :one
UPDATE flows
SET language = $1
WHERE id = $2
RETURNING id, status, title, model, model_provider, language, functions, prompts, user_id, created_at, updated_at, deleted_at, trace_id
`

type UpdateFlowLanguageParams struct {
	Language string `json:"language"`
	ID       int64  `json:"id"`
}

func (q *Queries) UpdateFlowLanguage(ctx context.Context, arg UpdateFlowLanguageParams) (Flow, error) {
	row := q.db.QueryRowContext(ctx, updateFlowLanguage, arg.Language, arg.ID)
	var i Flow
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Model,
		&i.ModelProvider,
		&i.Language,
		&i.Functions,
		&i.Prompts,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.TraceID,
	)
	return i, err
}

const updateFlowStatus = `-- name: UpdateFlowStatus :one
UPDATE flows
SET status = $1
WHERE id = $2
RETURNING id, status, title, model, model_provider, language, functions, prompts, user_id, created_at, updated_at, deleted_at, trace_id
`

type UpdateFlowStatusParams struct {
	Status FlowStatus `json:"status"`
	ID     int64      `json:"id"`
}

func (q *Queries) UpdateFlowStatus(ctx context.Context, arg UpdateFlowStatusParams) (Flow, error) {
	row := q.db.QueryRowContext(ctx, updateFlowStatus, arg.Status, arg.ID)
	var i Flow
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Model,
		&i.ModelProvider,
		&i.Language,
		&i.Functions,
		&i.Prompts,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.TraceID,
	)
	return i, err
}

const updateFlowTitle = `-- name: UpdateFlowTitle :one
UPDATE flows
SET title = $1
WHERE id = $2
RETURNING id, status, title, model, model_provider, language, functions, prompts, user_id, created_at, updated_at, deleted_at, trace_id
`

type UpdateFlowTitleParams struct {
	Title string `json:"title"`
	ID    int64  `json:"id"`
}

func (q *Queries) UpdateFlowTitle(ctx context.Context, arg UpdateFlowTitleParams) (Flow, error) {
	row := q.db.QueryRowContext(ctx, updateFlowTitle, arg.Title, arg.ID)
	var i Flow
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Model,
		&i.ModelProvider,
		&i.Language,
		&i.Functions,
		&i.Prompts,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.TraceID,
	)
	return i, err
}
