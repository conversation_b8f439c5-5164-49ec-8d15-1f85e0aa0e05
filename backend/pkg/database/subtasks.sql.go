// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0
// source: subtasks.sql

package database

import (
	"context"

	"github.com/lib/pq"
)

const createSubtask = `-- name: CreateSubtask :one
INSERT INTO subtasks (
  status,
  title,
  description,
  task_id
) VALUES (
  $1, $2, $3, $4
)
RETURNING id, status, title, description, result, task_id, created_at, updated_at, context
`

type CreateSubtaskParams struct {
	Status      SubtaskStatus `json:"status"`
	Title       string        `json:"title"`
	Description string        `json:"description"`
	TaskID      int64         `json:"task_id"`
}

func (q *Queries) CreateSubtask(ctx context.Context, arg CreateSubtaskParams) (Subtask, error) {
	row := q.db.QueryRowContext(ctx, createSubtask,
		arg.Status,
		arg.Title,
		arg.Description,
		arg.TaskID,
	)
	var i Subtask
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Description,
		&i.Result,
		&i.TaskID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Context,
	)
	return i, err
}

const deleteSubtask = `-- name: DeleteSubtask :exec
DELETE FROM subtasks
WHERE id = $1
`

func (q *Queries) DeleteSubtask(ctx context.Context, id int64) error {
	_, err := q.db.ExecContext(ctx, deleteSubtask, id)
	return err
}

const deleteSubtasks = `-- name: DeleteSubtasks :exec
DELETE FROM subtasks
WHERE id = ANY($1::BIGINT[])
`

func (q *Queries) DeleteSubtasks(ctx context.Context, ids []int64) error {
	_, err := q.db.ExecContext(ctx, deleteSubtasks, pq.Array(ids))
	return err
}

const getFlowSubtask = `-- name: GetFlowSubtask :one
SELECT
  s.id, s.status, s.title, s.description, s.result, s.task_id, s.created_at, s.updated_at, s.context
FROM subtasks s
INNER JOIN tasks t ON s.task_id = t.id
INNER JOIN flows f ON t.flow_id = f.id
WHERE s.id = $1 AND t.flow_id = $2 AND f.deleted_at IS NULL
`

type GetFlowSubtaskParams struct {
	ID     int64 `json:"id"`
	FlowID int64 `json:"flow_id"`
}

func (q *Queries) GetFlowSubtask(ctx context.Context, arg GetFlowSubtaskParams) (Subtask, error) {
	row := q.db.QueryRowContext(ctx, getFlowSubtask, arg.ID, arg.FlowID)
	var i Subtask
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Description,
		&i.Result,
		&i.TaskID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Context,
	)
	return i, err
}

const getFlowSubtasks = `-- name: GetFlowSubtasks :many
SELECT
  s.id, s.status, s.title, s.description, s.result, s.task_id, s.created_at, s.updated_at, s.context
FROM subtasks s
INNER JOIN tasks t ON s.task_id = t.id
INNER JOIN flows f ON t.flow_id = f.id
WHERE t.flow_id = $1 AND f.deleted_at IS NULL
ORDER BY s.created_at ASC
`

func (q *Queries) GetFlowSubtasks(ctx context.Context, flowID int64) ([]Subtask, error) {
	rows, err := q.db.QueryContext(ctx, getFlowSubtasks, flowID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Subtask
	for rows.Next() {
		var i Subtask
		if err := rows.Scan(
			&i.ID,
			&i.Status,
			&i.Title,
			&i.Description,
			&i.Result,
			&i.TaskID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Context,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getFlowTaskSubtasks = `-- name: GetFlowTaskSubtasks :many
SELECT
  s.id, s.status, s.title, s.description, s.result, s.task_id, s.created_at, s.updated_at, s.context
FROM subtasks s
INNER JOIN tasks t ON s.task_id = t.id
INNER JOIN flows f ON t.flow_id = f.id
WHERE s.task_id = $1 AND t.flow_id = $2 AND f.deleted_at IS NULL
ORDER BY s.created_at ASC
`

type GetFlowTaskSubtasksParams struct {
	TaskID int64 `json:"task_id"`
	FlowID int64 `json:"flow_id"`
}

func (q *Queries) GetFlowTaskSubtasks(ctx context.Context, arg GetFlowTaskSubtasksParams) ([]Subtask, error) {
	rows, err := q.db.QueryContext(ctx, getFlowTaskSubtasks, arg.TaskID, arg.FlowID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Subtask
	for rows.Next() {
		var i Subtask
		if err := rows.Scan(
			&i.ID,
			&i.Status,
			&i.Title,
			&i.Description,
			&i.Result,
			&i.TaskID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Context,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSubtask = `-- name: GetSubtask :one
SELECT
  s.id, s.status, s.title, s.description, s.result, s.task_id, s.created_at, s.updated_at, s.context
FROM subtasks s
WHERE s.id = $1
`

func (q *Queries) GetSubtask(ctx context.Context, id int64) (Subtask, error) {
	row := q.db.QueryRowContext(ctx, getSubtask, id)
	var i Subtask
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Description,
		&i.Result,
		&i.TaskID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Context,
	)
	return i, err
}

const getTaskCompletedSubtasks = `-- name: GetTaskCompletedSubtasks :many
SELECT
  s.id, s.status, s.title, s.description, s.result, s.task_id, s.created_at, s.updated_at, s.context
FROM subtasks s
INNER JOIN tasks t ON s.task_id = t.id
INNER JOIN flows f ON t.flow_id = f.id
WHERE s.task_id = $1 AND (s.status != 'created' AND s.status != 'waiting') AND f.deleted_at IS NULL
ORDER BY s.id ASC
`

func (q *Queries) GetTaskCompletedSubtasks(ctx context.Context, taskID int64) ([]Subtask, error) {
	rows, err := q.db.QueryContext(ctx, getTaskCompletedSubtasks, taskID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Subtask
	for rows.Next() {
		var i Subtask
		if err := rows.Scan(
			&i.ID,
			&i.Status,
			&i.Title,
			&i.Description,
			&i.Result,
			&i.TaskID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Context,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTaskPlannedSubtasks = `-- name: GetTaskPlannedSubtasks :many
SELECT
  s.id, s.status, s.title, s.description, s.result, s.task_id, s.created_at, s.updated_at, s.context
FROM subtasks s
INNER JOIN tasks t ON s.task_id = t.id
INNER JOIN flows f ON t.flow_id = f.id
WHERE s.task_id = $1 AND (s.status = 'created' OR s.status = 'waiting') AND f.deleted_at IS NULL
ORDER BY s.id ASC
`

func (q *Queries) GetTaskPlannedSubtasks(ctx context.Context, taskID int64) ([]Subtask, error) {
	rows, err := q.db.QueryContext(ctx, getTaskPlannedSubtasks, taskID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Subtask
	for rows.Next() {
		var i Subtask
		if err := rows.Scan(
			&i.ID,
			&i.Status,
			&i.Title,
			&i.Description,
			&i.Result,
			&i.TaskID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Context,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTaskSubtasks = `-- name: GetTaskSubtasks :many
SELECT
  s.id, s.status, s.title, s.description, s.result, s.task_id, s.created_at, s.updated_at, s.context
FROM subtasks s
INNER JOIN tasks t ON s.task_id = t.id
INNER JOIN flows f ON t.flow_id = f.id
WHERE s.task_id = $1 AND f.deleted_at IS NULL
ORDER BY s.created_at DESC
`

func (q *Queries) GetTaskSubtasks(ctx context.Context, taskID int64) ([]Subtask, error) {
	rows, err := q.db.QueryContext(ctx, getTaskSubtasks, taskID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Subtask
	for rows.Next() {
		var i Subtask
		if err := rows.Scan(
			&i.ID,
			&i.Status,
			&i.Title,
			&i.Description,
			&i.Result,
			&i.TaskID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Context,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserFlowSubtasks = `-- name: GetUserFlowSubtasks :many
SELECT
  s.id, s.status, s.title, s.description, s.result, s.task_id, s.created_at, s.updated_at, s.context
FROM subtasks s
INNER JOIN tasks t ON s.task_id = t.id
INNER JOIN flows f ON t.flow_id = f.id
INNER JOIN users u ON f.user_id = u.id
WHERE t.flow_id = $1 AND f.user_id = $2 AND f.deleted_at IS NULL
ORDER BY s.created_at ASC
`

type GetUserFlowSubtasksParams struct {
	FlowID int64 `json:"flow_id"`
	UserID int64 `json:"user_id"`
}

func (q *Queries) GetUserFlowSubtasks(ctx context.Context, arg GetUserFlowSubtasksParams) ([]Subtask, error) {
	rows, err := q.db.QueryContext(ctx, getUserFlowSubtasks, arg.FlowID, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Subtask
	for rows.Next() {
		var i Subtask
		if err := rows.Scan(
			&i.ID,
			&i.Status,
			&i.Title,
			&i.Description,
			&i.Result,
			&i.TaskID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Context,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserFlowTaskSubtasks = `-- name: GetUserFlowTaskSubtasks :many
SELECT
  s.id, s.status, s.title, s.description, s.result, s.task_id, s.created_at, s.updated_at, s.context
FROM subtasks s
INNER JOIN tasks t ON s.task_id = t.id
INNER JOIN flows f ON t.flow_id = f.id
INNER JOIN users u ON f.user_id = u.id
WHERE s.task_id = $1 AND t.flow_id = $2 AND f.user_id = $3 AND f.deleted_at IS NULL
ORDER BY s.created_at ASC
`

type GetUserFlowTaskSubtasksParams struct {
	TaskID int64 `json:"task_id"`
	FlowID int64 `json:"flow_id"`
	UserID int64 `json:"user_id"`
}

func (q *Queries) GetUserFlowTaskSubtasks(ctx context.Context, arg GetUserFlowTaskSubtasksParams) ([]Subtask, error) {
	rows, err := q.db.QueryContext(ctx, getUserFlowTaskSubtasks, arg.TaskID, arg.FlowID, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Subtask
	for rows.Next() {
		var i Subtask
		if err := rows.Scan(
			&i.ID,
			&i.Status,
			&i.Title,
			&i.Description,
			&i.Result,
			&i.TaskID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Context,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateSubtaskContext = `-- name: UpdateSubtaskContext :one
UPDATE subtasks
SET context = $1
WHERE id = $2
RETURNING id, status, title, description, result, task_id, created_at, updated_at, context
`

type UpdateSubtaskContextParams struct {
	Context string `json:"context"`
	ID      int64  `json:"id"`
}

func (q *Queries) UpdateSubtaskContext(ctx context.Context, arg UpdateSubtaskContextParams) (Subtask, error) {
	row := q.db.QueryRowContext(ctx, updateSubtaskContext, arg.Context, arg.ID)
	var i Subtask
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Description,
		&i.Result,
		&i.TaskID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Context,
	)
	return i, err
}

const updateSubtaskFailedResult = `-- name: UpdateSubtaskFailedResult :one
UPDATE subtasks
SET status = 'failed', result = $1
WHERE id = $2
RETURNING id, status, title, description, result, task_id, created_at, updated_at, context
`

type UpdateSubtaskFailedResultParams struct {
	Result string `json:"result"`
	ID     int64  `json:"id"`
}

func (q *Queries) UpdateSubtaskFailedResult(ctx context.Context, arg UpdateSubtaskFailedResultParams) (Subtask, error) {
	row := q.db.QueryRowContext(ctx, updateSubtaskFailedResult, arg.Result, arg.ID)
	var i Subtask
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Description,
		&i.Result,
		&i.TaskID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Context,
	)
	return i, err
}

const updateSubtaskFinishedResult = `-- name: UpdateSubtaskFinishedResult :one
UPDATE subtasks
SET status = 'finished', result = $1
WHERE id = $2
RETURNING id, status, title, description, result, task_id, created_at, updated_at, context
`

type UpdateSubtaskFinishedResultParams struct {
	Result string `json:"result"`
	ID     int64  `json:"id"`
}

func (q *Queries) UpdateSubtaskFinishedResult(ctx context.Context, arg UpdateSubtaskFinishedResultParams) (Subtask, error) {
	row := q.db.QueryRowContext(ctx, updateSubtaskFinishedResult, arg.Result, arg.ID)
	var i Subtask
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Description,
		&i.Result,
		&i.TaskID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Context,
	)
	return i, err
}

const updateSubtaskResult = `-- name: UpdateSubtaskResult :one
UPDATE subtasks
SET result = $1
WHERE id = $2
RETURNING id, status, title, description, result, task_id, created_at, updated_at, context
`

type UpdateSubtaskResultParams struct {
	Result string `json:"result"`
	ID     int64  `json:"id"`
}

func (q *Queries) UpdateSubtaskResult(ctx context.Context, arg UpdateSubtaskResultParams) (Subtask, error) {
	row := q.db.QueryRowContext(ctx, updateSubtaskResult, arg.Result, arg.ID)
	var i Subtask
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Description,
		&i.Result,
		&i.TaskID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Context,
	)
	return i, err
}

const updateSubtaskStatus = `-- name: UpdateSubtaskStatus :one
UPDATE subtasks
SET status = $1
WHERE id = $2
RETURNING id, status, title, description, result, task_id, created_at, updated_at, context
`

type UpdateSubtaskStatusParams struct {
	Status SubtaskStatus `json:"status"`
	ID     int64         `json:"id"`
}

func (q *Queries) UpdateSubtaskStatus(ctx context.Context, arg UpdateSubtaskStatusParams) (Subtask, error) {
	row := q.db.QueryRowContext(ctx, updateSubtaskStatus, arg.Status, arg.ID)
	var i Subtask
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Description,
		&i.Result,
		&i.TaskID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Context,
	)
	return i, err
}
