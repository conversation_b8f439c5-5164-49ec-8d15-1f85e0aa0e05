// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0

package database

import (
	"context"
	"database/sql"
)

type Querier interface {
	CreateAgentLog(ctx context.Context, arg CreateAgentLogParams) (Agentlog, error)
	CreateAssistant(ctx context.Context, arg CreateAssistantParams) (Assistant, error)
	CreateAssistantLog(ctx context.Context, arg CreateAssistantLogParams) (Assistantlog, error)
	CreateContainer(ctx context.Context, arg CreateContainerParams) (Container, error)
	CreateFlow(ctx context.Context, arg CreateFlowParams) (Flow, error)
	CreateMsgChain(ctx context.Context, arg CreateMsgChainParams) (Msgchain, error)
	CreateMsgLog(ctx context.Context, arg CreateMsgLogParams) (Msglog, error)
	CreateResultAssistantLog(ctx context.Context, arg CreateResultAssistantLogParams) (Assistantlog, error)
	CreateResultMsgLog(ctx context.Context, arg CreateResultMsgLogParams) (Msglog, error)
	CreateScreenshot(ctx context.Context, arg CreateScreenshotParams) (Screenshot, error)
	CreateSearchLog(ctx context.Context, arg CreateSearchLogParams) (Searchlog, error)
	CreateSubtask(ctx context.Context, arg CreateSubtaskParams) (Subtask, error)
	CreateTask(ctx context.Context, arg CreateTaskParams) (Task, error)
	CreateTermLog(ctx context.Context, arg CreateTermLogParams) (Termlog, error)
	CreateToolcall(ctx context.Context, arg CreateToolcallParams) (Toolcall, error)
	CreateUser(ctx context.Context, arg CreateUserParams) (User, error)
	CreateUserPrompt(ctx context.Context, arg CreateUserPromptParams) (Prompt, error)
	CreateVectorStoreLog(ctx context.Context, arg CreateVectorStoreLogParams) (Vecstorelog, error)
	DeleteAssistant(ctx context.Context, id int64) (Assistant, error)
	DeleteFlow(ctx context.Context, id int64) (Flow, error)
	DeleteSubtask(ctx context.Context, id int64) error
	DeleteSubtasks(ctx context.Context, ids []int64) error
	DeleteUser(ctx context.Context, id int64) error
	GetAssistant(ctx context.Context, id int64) (Assistant, error)
	GetAssistantUseAgents(ctx context.Context, id int64) (bool, error)
	GetCallToolcall(ctx context.Context, callID string) (Toolcall, error)
	GetContainers(ctx context.Context) ([]Container, error)
	GetFlow(ctx context.Context, id int64) (Flow, error)
	GetFlowAgentLog(ctx context.Context, arg GetFlowAgentLogParams) (Agentlog, error)
	GetFlowAgentLogs(ctx context.Context, flowID int64) ([]Agentlog, error)
	GetFlowAssistant(ctx context.Context, arg GetFlowAssistantParams) (Assistant, error)
	GetFlowAssistantLog(ctx context.Context, id int64) (Assistantlog, error)
	GetFlowAssistantLogs(ctx context.Context, arg GetFlowAssistantLogsParams) ([]Assistantlog, error)
	GetFlowAssistants(ctx context.Context, flowID int64) ([]Assistant, error)
	GetFlowContainers(ctx context.Context, flowID int64) ([]Container, error)
	GetFlowMsgChains(ctx context.Context, flowID int64) ([]Msgchain, error)
	GetFlowMsgLogs(ctx context.Context, flowID int64) ([]Msglog, error)
	GetFlowPrimaryContainer(ctx context.Context, flowID int64) (Container, error)
	GetFlowScreenshots(ctx context.Context, flowID int64) ([]Screenshot, error)
	GetFlowSearchLog(ctx context.Context, arg GetFlowSearchLogParams) (Searchlog, error)
	GetFlowSearchLogs(ctx context.Context, flowID int64) ([]Searchlog, error)
	GetFlowSubtask(ctx context.Context, arg GetFlowSubtaskParams) (Subtask, error)
	GetFlowSubtasks(ctx context.Context, flowID int64) ([]Subtask, error)
	GetFlowTask(ctx context.Context, arg GetFlowTaskParams) (Task, error)
	GetFlowTaskSubtasks(ctx context.Context, arg GetFlowTaskSubtasksParams) ([]Subtask, error)
	GetFlowTaskTypeLastMsgChain(ctx context.Context, arg GetFlowTaskTypeLastMsgChainParams) (Msgchain, error)
	GetFlowTasks(ctx context.Context, flowID int64) ([]Task, error)
	GetFlowTermLogs(ctx context.Context, flowID int64) ([]Termlog, error)
	GetFlowTypeMsgChains(ctx context.Context, arg GetFlowTypeMsgChainsParams) ([]Msgchain, error)
	GetFlowVectorStoreLog(ctx context.Context, arg GetFlowVectorStoreLogParams) (Vecstorelog, error)
	GetFlowVectorStoreLogs(ctx context.Context, flowID int64) ([]Vecstorelog, error)
	GetFlows(ctx context.Context) ([]Flow, error)
	GetMsgChain(ctx context.Context, id int64) (Msgchain, error)
	GetRole(ctx context.Context, id int64) (GetRoleRow, error)
	GetRoleByName(ctx context.Context, name string) (GetRoleByNameRow, error)
	GetRoles(ctx context.Context) ([]GetRolesRow, error)
	GetRunningContainers(ctx context.Context) ([]Container, error)
	GetScreenshot(ctx context.Context, id int64) (Screenshot, error)
	GetSubtask(ctx context.Context, id int64) (Subtask, error)
	GetSubtaskAgentLogs(ctx context.Context, subtaskID sql.NullInt64) ([]Agentlog, error)
	GetSubtaskMsgChains(ctx context.Context, subtaskID sql.NullInt64) ([]Msgchain, error)
	GetSubtaskMsgLogs(ctx context.Context, subtaskID sql.NullInt64) ([]Msglog, error)
	GetSubtaskPrimaryMsgChains(ctx context.Context, subtaskID sql.NullInt64) ([]Msgchain, error)
	GetSubtaskSearchLogs(ctx context.Context, subtaskID sql.NullInt64) ([]Searchlog, error)
	GetSubtaskToolcalls(ctx context.Context, subtaskID sql.NullInt64) ([]Toolcall, error)
	GetSubtaskTypeMsgChains(ctx context.Context, arg GetSubtaskTypeMsgChainsParams) ([]Msgchain, error)
	GetSubtaskVectorStoreLogs(ctx context.Context, subtaskID sql.NullInt64) ([]Vecstorelog, error)
	GetTask(ctx context.Context, id int64) (Task, error)
	GetTaskAgentLogs(ctx context.Context, taskID sql.NullInt64) ([]Agentlog, error)
	GetTaskCompletedSubtasks(ctx context.Context, taskID int64) ([]Subtask, error)
	GetTaskMsgChains(ctx context.Context, taskID sql.NullInt64) ([]Msgchain, error)
	GetTaskMsgLogs(ctx context.Context, taskID sql.NullInt64) ([]Msglog, error)
	GetTaskPlannedSubtasks(ctx context.Context, taskID int64) ([]Subtask, error)
	GetTaskPrimaryMsgChainIDs(ctx context.Context, taskID sql.NullInt64) ([]GetTaskPrimaryMsgChainIDsRow, error)
	GetTaskPrimaryMsgChains(ctx context.Context, taskID sql.NullInt64) ([]Msgchain, error)
	GetTaskSearchLogs(ctx context.Context, taskID sql.NullInt64) ([]Searchlog, error)
	GetTaskSubtasks(ctx context.Context, taskID int64) ([]Subtask, error)
	GetTaskTypeMsgChains(ctx context.Context, arg GetTaskTypeMsgChainsParams) ([]Msgchain, error)
	GetTaskVectorStoreLogs(ctx context.Context, taskID sql.NullInt64) ([]Vecstorelog, error)
	GetTermLog(ctx context.Context, id int64) (Termlog, error)
	GetUser(ctx context.Context, id int64) (GetUserRow, error)
	GetUserByHash(ctx context.Context, hash string) (GetUserByHashRow, error)
	GetUserContainers(ctx context.Context, userID int64) ([]Container, error)
	GetUserFlow(ctx context.Context, arg GetUserFlowParams) (Flow, error)
	GetUserFlowAgentLogs(ctx context.Context, arg GetUserFlowAgentLogsParams) ([]Agentlog, error)
	GetUserFlowAssistant(ctx context.Context, arg GetUserFlowAssistantParams) (Assistant, error)
	GetUserFlowAssistantLogs(ctx context.Context, arg GetUserFlowAssistantLogsParams) ([]Assistantlog, error)
	GetUserFlowAssistants(ctx context.Context, arg GetUserFlowAssistantsParams) ([]Assistant, error)
	GetUserFlowContainers(ctx context.Context, arg GetUserFlowContainersParams) ([]Container, error)
	GetUserFlowMsgLogs(ctx context.Context, arg GetUserFlowMsgLogsParams) ([]Msglog, error)
	GetUserFlowScreenshots(ctx context.Context, arg GetUserFlowScreenshotsParams) ([]Screenshot, error)
	GetUserFlowSearchLogs(ctx context.Context, arg GetUserFlowSearchLogsParams) ([]Searchlog, error)
	GetUserFlowSubtasks(ctx context.Context, arg GetUserFlowSubtasksParams) ([]Subtask, error)
	GetUserFlowTask(ctx context.Context, arg GetUserFlowTaskParams) (Task, error)
	GetUserFlowTaskSubtasks(ctx context.Context, arg GetUserFlowTaskSubtasksParams) ([]Subtask, error)
	GetUserFlowTasks(ctx context.Context, arg GetUserFlowTasksParams) ([]Task, error)
	GetUserFlowTermLogs(ctx context.Context, arg GetUserFlowTermLogsParams) ([]Termlog, error)
	GetUserFlowVectorStoreLogs(ctx context.Context, arg GetUserFlowVectorStoreLogsParams) ([]Vecstorelog, error)
	GetUserFlows(ctx context.Context, userID int64) ([]Flow, error)
	GetUserPrompts(ctx context.Context, userID int64) ([]Prompt, error)
	GetUserTypePrompt(ctx context.Context, arg GetUserTypePromptParams) (Prompt, error)
	GetUsers(ctx context.Context) ([]GetUsersRow, error)
	UpdateAssistant(ctx context.Context, arg UpdateAssistantParams) (Assistant, error)
	UpdateAssistantLanguage(ctx context.Context, arg UpdateAssistantLanguageParams) (Assistant, error)
	UpdateAssistantLog(ctx context.Context, arg UpdateAssistantLogParams) (Assistantlog, error)
	UpdateAssistantLogContent(ctx context.Context, arg UpdateAssistantLogContentParams) (Assistantlog, error)
	UpdateAssistantLogResult(ctx context.Context, arg UpdateAssistantLogResultParams) (Assistantlog, error)
	UpdateAssistantModel(ctx context.Context, arg UpdateAssistantModelParams) (Assistant, error)
	UpdateAssistantStatus(ctx context.Context, arg UpdateAssistantStatusParams) (Assistant, error)
	UpdateAssistantTitle(ctx context.Context, arg UpdateAssistantTitleParams) (Assistant, error)
	UpdateAssistantUseAgents(ctx context.Context, arg UpdateAssistantUseAgentsParams) (Assistant, error)
	UpdateContainerImage(ctx context.Context, arg UpdateContainerImageParams) (Container, error)
	UpdateContainerLocalDir(ctx context.Context, arg UpdateContainerLocalDirParams) (Container, error)
	UpdateContainerLocalID(ctx context.Context, arg UpdateContainerLocalIDParams) (Container, error)
	UpdateContainerStatus(ctx context.Context, arg UpdateContainerStatusParams) (Container, error)
	UpdateContainerStatusLocalID(ctx context.Context, arg UpdateContainerStatusLocalIDParams) (Container, error)
	UpdateFlow(ctx context.Context, arg UpdateFlowParams) (Flow, error)
	UpdateFlowLanguage(ctx context.Context, arg UpdateFlowLanguageParams) (Flow, error)
	UpdateFlowStatus(ctx context.Context, arg UpdateFlowStatusParams) (Flow, error)
	UpdateFlowTitle(ctx context.Context, arg UpdateFlowTitleParams) (Flow, error)
	UpdateMsgChain(ctx context.Context, arg UpdateMsgChainParams) (Msgchain, error)
	UpdateMsgChainUsage(ctx context.Context, arg UpdateMsgChainUsageParams) (Msgchain, error)
	UpdateMsgLogResult(ctx context.Context, arg UpdateMsgLogResultParams) (Msglog, error)
	UpdateSubtaskContext(ctx context.Context, arg UpdateSubtaskContextParams) (Subtask, error)
	UpdateSubtaskFailedResult(ctx context.Context, arg UpdateSubtaskFailedResultParams) (Subtask, error)
	UpdateSubtaskFinishedResult(ctx context.Context, arg UpdateSubtaskFinishedResultParams) (Subtask, error)
	UpdateSubtaskResult(ctx context.Context, arg UpdateSubtaskResultParams) (Subtask, error)
	UpdateSubtaskStatus(ctx context.Context, arg UpdateSubtaskStatusParams) (Subtask, error)
	UpdateTaskFailedResult(ctx context.Context, arg UpdateTaskFailedResultParams) (Task, error)
	UpdateTaskFinishedResult(ctx context.Context, arg UpdateTaskFinishedResultParams) (Task, error)
	UpdateTaskResult(ctx context.Context, arg UpdateTaskResultParams) (Task, error)
	UpdateTaskStatus(ctx context.Context, arg UpdateTaskStatusParams) (Task, error)
	UpdateToolcallFailedResult(ctx context.Context, arg UpdateToolcallFailedResultParams) (Toolcall, error)
	UpdateToolcallFinishedResult(ctx context.Context, arg UpdateToolcallFinishedResultParams) (Toolcall, error)
	UpdateToolcallStatus(ctx context.Context, arg UpdateToolcallStatusParams) (Toolcall, error)
	UpdateUserName(ctx context.Context, arg UpdateUserNameParams) (User, error)
	UpdateUserPassword(ctx context.Context, arg UpdateUserPasswordParams) (User, error)
	UpdateUserPasswordChangeRequired(ctx context.Context, arg UpdateUserPasswordChangeRequiredParams) (User, error)
	UpdateUserRole(ctx context.Context, arg UpdateUserRoleParams) (User, error)
	UpdateUserStatus(ctx context.Context, arg UpdateUserStatusParams) (User, error)
	UpdateUserTypePrompt(ctx context.Context, arg UpdateUserTypePromptParams) (Prompt, error)
}

var _ Querier = (*Queries)(nil)
