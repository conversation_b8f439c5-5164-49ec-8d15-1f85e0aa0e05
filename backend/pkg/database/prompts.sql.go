// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0
// source: prompts.sql

package database

import (
	"context"
)

const createUserPrompt = `-- name: CreateUserPrompt :one
INSERT INTO prompts (
  type,
  user_id,
  prompt
) VALUES (
  $1, $2, $3
)
RETURNING id, type, user_id, prompt
`

type CreateUserPromptParams struct {
	Type   string `json:"type"`
	UserID int64  `json:"user_id"`
	Prompt string `json:"prompt"`
}

func (q *Queries) CreateUserPrompt(ctx context.Context, arg CreateUserPromptParams) (Prompt, error) {
	row := q.db.QueryRowContext(ctx, createUserPrompt, arg.Type, arg.UserID, arg.Prompt)
	var i Prompt
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.UserID,
		&i.Prompt,
	)
	return i, err
}

const getUserPrompts = `-- name: GetUserPrompts :many
SELECT
  p.id, p.type, p.user_id, p.prompt
FROM prompts p
INNER JOIN users u ON p.user_id = u.id
WHERE p.user_id = $1
ORDER BY p.type ASC
`

func (q *Queries) GetUserPrompts(ctx context.Context, userID int64) ([]Prompt, error) {
	rows, err := q.db.QueryContext(ctx, getUserPrompts, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Prompt
	for rows.Next() {
		var i Prompt
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.UserID,
			&i.Prompt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserTypePrompt = `-- name: GetUserTypePrompt :one
SELECT
  p.id, p.type, p.user_id, p.prompt
FROM prompts p
INNER JOIN users u ON p.user_id = u.id
WHERE p.type = $1 AND p.user_id = $2
`

type GetUserTypePromptParams struct {
	Type   string `json:"type"`
	UserID int64  `json:"user_id"`
}

func (q *Queries) GetUserTypePrompt(ctx context.Context, arg GetUserTypePromptParams) (Prompt, error) {
	row := q.db.QueryRowContext(ctx, getUserTypePrompt, arg.Type, arg.UserID)
	var i Prompt
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.UserID,
		&i.Prompt,
	)
	return i, err
}

const updateUserTypePrompt = `-- name: UpdateUserTypePrompt :one
UPDATE prompts
SET prompt = $1
WHERE type = $2 AND user_id = $3
RETURNING id, type, user_id, prompt
`

type UpdateUserTypePromptParams struct {
	Prompt string `json:"prompt"`
	Type   string `json:"type"`
	UserID int64  `json:"user_id"`
}

func (q *Queries) UpdateUserTypePrompt(ctx context.Context, arg UpdateUserTypePromptParams) (Prompt, error) {
	row := q.db.QueryRowContext(ctx, updateUserTypePrompt, arg.Prompt, arg.Type, arg.UserID)
	var i Prompt
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.UserID,
		&i.Prompt,
	)
	return i, err
}
