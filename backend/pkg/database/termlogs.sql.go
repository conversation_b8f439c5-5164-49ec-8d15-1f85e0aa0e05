// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0
// source: termlogs.sql

package database

import (
	"context"
)

const createTermLog = `-- name: CreateTermLog :one
INSERT INTO termlogs (
  type,
  text,
  container_id
)
VALUES (
  $1, $2, $3
)
RETURNING id, type, text, container_id, created_at
`

type CreateTermLogParams struct {
	Type        TermlogType `json:"type"`
	Text        string      `json:"text"`
	ContainerID int64       `json:"container_id"`
}

func (q *Queries) CreateTermLog(ctx context.Context, arg CreateTermLogParams) (Termlog, error) {
	row := q.db.QueryRowContext(ctx, createTermLog, arg.Type, arg.Text, arg.ContainerID)
	var i Termlog
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Text,
		&i.ContainerID,
		&i.CreatedAt,
	)
	return i, err
}

const getFlowTermLogs = `-- name: GetFlowTermLogs :many
SELECT
  tl.id, tl.type, tl.text, tl.container_id, tl.created_at
FROM termlogs tl
INNER JOIN containers c ON tl.container_id = c.id
INNER JOIN flows f ON c.flow_id = f.id
WHERE c.flow_id = $1
ORDER BY tl.created_at ASC
`

func (q *Queries) GetFlowTermLogs(ctx context.Context, flowID int64) ([]Termlog, error) {
	rows, err := q.db.QueryContext(ctx, getFlowTermLogs, flowID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Termlog
	for rows.Next() {
		var i Termlog
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Text,
			&i.ContainerID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTermLog = `-- name: GetTermLog :one
SELECT
  tl.id, tl.type, tl.text, tl.container_id, tl.created_at
FROM termlogs tl
WHERE tl.id = $1
`

func (q *Queries) GetTermLog(ctx context.Context, id int64) (Termlog, error) {
	row := q.db.QueryRowContext(ctx, getTermLog, id)
	var i Termlog
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Text,
		&i.ContainerID,
		&i.CreatedAt,
	)
	return i, err
}

const getUserFlowTermLogs = `-- name: GetUserFlowTermLogs :many
SELECT
  tl.id, tl.type, tl.text, tl.container_id, tl.created_at
FROM termlogs tl
INNER JOIN containers c ON tl.container_id = c.id
INNER JOIN flows f ON c.flow_id = f.id
INNER JOIN users u ON f.user_id = u.id
WHERE c.flow_id = $1 AND f.user_id = $2
ORDER BY tl.created_at ASC
`

type GetUserFlowTermLogsParams struct {
	FlowID int64 `json:"flow_id"`
	UserID int64 `json:"user_id"`
}

func (q *Queries) GetUserFlowTermLogs(ctx context.Context, arg GetUserFlowTermLogsParams) ([]Termlog, error) {
	rows, err := q.db.QueryContext(ctx, getUserFlowTermLogs, arg.FlowID, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Termlog
	for rows.Next() {
		var i Termlog
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Text,
			&i.ContainerID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
