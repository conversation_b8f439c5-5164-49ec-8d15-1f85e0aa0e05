// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0
// source: toolcalls.sql

package database

import (
	"context"
	"database/sql"
	"encoding/json"
)

const createToolcall = `-- name: CreateToolcall :one
INSERT INTO toolcalls (
  call_id,
  status,
  name,
  args,
  flow_id,
  task_id,
  subtask_id
) VALUES (
  $1, $2, $3, $4, $5, $6, $7
)
RETURNING id, call_id, status, name, args, result, flow_id, task_id, subtask_id, created_at, updated_at
`

type CreateToolcallParams struct {
	CallID    string          `json:"call_id"`
	Status    ToolcallStatus  `json:"status"`
	Name      string          `json:"name"`
	Args      json.RawMessage `json:"args"`
	FlowID    int64           `json:"flow_id"`
	TaskID    sql.NullInt64   `json:"task_id"`
	SubtaskID sql.NullInt64   `json:"subtask_id"`
}

func (q *Queries) CreateToolcall(ctx context.Context, arg CreateToolcallParams) (Toolcall, error) {
	row := q.db.QueryRowContext(ctx, createToolcall,
		arg.CallID,
		arg.Status,
		arg.Name,
		arg.Args,
		arg.FlowID,
		arg.TaskID,
		arg.SubtaskID,
	)
	var i Toolcall
	err := row.Scan(
		&i.ID,
		&i.CallID,
		&i.Status,
		&i.Name,
		&i.Args,
		&i.Result,
		&i.FlowID,
		&i.TaskID,
		&i.SubtaskID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getCallToolcall = `-- name: GetCallToolcall :one
SELECT
  tc.id, tc.call_id, tc.status, tc.name, tc.args, tc.result, tc.flow_id, tc.task_id, tc.subtask_id, tc.created_at, tc.updated_at
FROM toolcalls tc
WHERE tc.call_id = $1
`

func (q *Queries) GetCallToolcall(ctx context.Context, callID string) (Toolcall, error) {
	row := q.db.QueryRowContext(ctx, getCallToolcall, callID)
	var i Toolcall
	err := row.Scan(
		&i.ID,
		&i.CallID,
		&i.Status,
		&i.Name,
		&i.Args,
		&i.Result,
		&i.FlowID,
		&i.TaskID,
		&i.SubtaskID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getSubtaskToolcalls = `-- name: GetSubtaskToolcalls :many
SELECT
  tc.id, tc.call_id, tc.status, tc.name, tc.args, tc.result, tc.flow_id, tc.task_id, tc.subtask_id, tc.created_at, tc.updated_at
FROM toolcalls tc
INNER JOIN subtasks s ON tc.subtask_id = s.id
INNER JOIN tasks t ON s.task_id = t.id
INNER JOIN flows f ON t.flow_id = f.id
WHERE tc.subtask_id = $1
ORDER BY tc.created_at DESC
`

func (q *Queries) GetSubtaskToolcalls(ctx context.Context, subtaskID sql.NullInt64) ([]Toolcall, error) {
	rows, err := q.db.QueryContext(ctx, getSubtaskToolcalls, subtaskID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Toolcall
	for rows.Next() {
		var i Toolcall
		if err := rows.Scan(
			&i.ID,
			&i.CallID,
			&i.Status,
			&i.Name,
			&i.Args,
			&i.Result,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateToolcallFailedResult = `-- name: UpdateToolcallFailedResult :one
UPDATE toolcalls
SET status = 'failed', result = $1
WHERE id = $2
RETURNING id, call_id, status, name, args, result, flow_id, task_id, subtask_id, created_at, updated_at
`

type UpdateToolcallFailedResultParams struct {
	Result string `json:"result"`
	ID     int64  `json:"id"`
}

func (q *Queries) UpdateToolcallFailedResult(ctx context.Context, arg UpdateToolcallFailedResultParams) (Toolcall, error) {
	row := q.db.QueryRowContext(ctx, updateToolcallFailedResult, arg.Result, arg.ID)
	var i Toolcall
	err := row.Scan(
		&i.ID,
		&i.CallID,
		&i.Status,
		&i.Name,
		&i.Args,
		&i.Result,
		&i.FlowID,
		&i.TaskID,
		&i.SubtaskID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateToolcallFinishedResult = `-- name: UpdateToolcallFinishedResult :one
UPDATE toolcalls
SET status = 'finished', result = $1
WHERE id = $2
RETURNING id, call_id, status, name, args, result, flow_id, task_id, subtask_id, created_at, updated_at
`

type UpdateToolcallFinishedResultParams struct {
	Result string `json:"result"`
	ID     int64  `json:"id"`
}

func (q *Queries) UpdateToolcallFinishedResult(ctx context.Context, arg UpdateToolcallFinishedResultParams) (Toolcall, error) {
	row := q.db.QueryRowContext(ctx, updateToolcallFinishedResult, arg.Result, arg.ID)
	var i Toolcall
	err := row.Scan(
		&i.ID,
		&i.CallID,
		&i.Status,
		&i.Name,
		&i.Args,
		&i.Result,
		&i.FlowID,
		&i.TaskID,
		&i.SubtaskID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateToolcallStatus = `-- name: UpdateToolcallStatus :one
UPDATE toolcalls
SET status = $1
WHERE id = $2
RETURNING id, call_id, status, name, args, result, flow_id, task_id, subtask_id, created_at, updated_at
`

type UpdateToolcallStatusParams struct {
	Status ToolcallStatus `json:"status"`
	ID     int64          `json:"id"`
}

func (q *Queries) UpdateToolcallStatus(ctx context.Context, arg UpdateToolcallStatusParams) (Toolcall, error) {
	row := q.db.QueryRowContext(ctx, updateToolcallStatus, arg.Status, arg.ID)
	var i Toolcall
	err := row.Scan(
		&i.ID,
		&i.CallID,
		&i.Status,
		&i.Name,
		&i.Args,
		&i.Result,
		&i.FlowID,
		&i.TaskID,
		&i.SubtaskID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
