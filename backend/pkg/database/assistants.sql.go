// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0
// source: assistants.sql

package database

import (
	"context"
	"database/sql"
	"encoding/json"
)

const createAssistant = `-- name: CreateAssistant :one
INSERT INTO assistants (
  title, status, model, model_provider, language, functions, prompts, flow_id, use_agents
) VALUES (
  $1, $2, $3, $4, $5, $6, $7, $8, $9
)
RETURNING id, status, title, model, model_provider, language, functions, prompts, trace_id, flow_id, use_agents, msgchain_id, created_at, updated_at, deleted_at
`

type CreateAssistantParams struct {
	Title         string          `json:"title"`
	Status        AssistantStatus `json:"status"`
	Model         string          `json:"model"`
	ModelProvider string          `json:"model_provider"`
	Language      string          `json:"language"`
	Functions     json.RawMessage `json:"functions"`
	Prompts       json.RawMessage `json:"prompts"`
	FlowID        int64           `json:"flow_id"`
	UseAgents     bool            `json:"use_agents"`
}

func (q *Queries) CreateAssistant(ctx context.Context, arg CreateAssistantParams) (Assistant, error) {
	row := q.db.QueryRowContext(ctx, createAssistant,
		arg.Title,
		arg.Status,
		arg.Model,
		arg.ModelProvider,
		arg.Language,
		arg.Functions,
		arg.Prompts,
		arg.FlowID,
		arg.UseAgents,
	)
	var i Assistant
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Model,
		&i.ModelProvider,
		&i.Language,
		&i.Functions,
		&i.Prompts,
		&i.TraceID,
		&i.FlowID,
		&i.UseAgents,
		&i.MsgchainID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const deleteAssistant = `-- name: DeleteAssistant :one
UPDATE assistants
SET deleted_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, status, title, model, model_provider, language, functions, prompts, trace_id, flow_id, use_agents, msgchain_id, created_at, updated_at, deleted_at
`

func (q *Queries) DeleteAssistant(ctx context.Context, id int64) (Assistant, error) {
	row := q.db.QueryRowContext(ctx, deleteAssistant, id)
	var i Assistant
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Model,
		&i.ModelProvider,
		&i.Language,
		&i.Functions,
		&i.Prompts,
		&i.TraceID,
		&i.FlowID,
		&i.UseAgents,
		&i.MsgchainID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getAssistant = `-- name: GetAssistant :one
SELECT
  a.id, a.status, a.title, a.model, a.model_provider, a.language, a.functions, a.prompts, a.trace_id, a.flow_id, a.use_agents, a.msgchain_id, a.created_at, a.updated_at, a.deleted_at
FROM assistants a
WHERE a.id = $1 AND a.deleted_at IS NULL
`

func (q *Queries) GetAssistant(ctx context.Context, id int64) (Assistant, error) {
	row := q.db.QueryRowContext(ctx, getAssistant, id)
	var i Assistant
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Model,
		&i.ModelProvider,
		&i.Language,
		&i.Functions,
		&i.Prompts,
		&i.TraceID,
		&i.FlowID,
		&i.UseAgents,
		&i.MsgchainID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getAssistantUseAgents = `-- name: GetAssistantUseAgents :one
SELECT use_agents
FROM assistants
WHERE id = $1 AND deleted_at IS NULL
`

func (q *Queries) GetAssistantUseAgents(ctx context.Context, id int64) (bool, error) {
	row := q.db.QueryRowContext(ctx, getAssistantUseAgents, id)
	var use_agents bool
	err := row.Scan(&use_agents)
	return use_agents, err
}

const getFlowAssistant = `-- name: GetFlowAssistant :one
SELECT
  a.id, a.status, a.title, a.model, a.model_provider, a.language, a.functions, a.prompts, a.trace_id, a.flow_id, a.use_agents, a.msgchain_id, a.created_at, a.updated_at, a.deleted_at
FROM assistants a
INNER JOIN flows f ON a.flow_id = f.id
WHERE a.id = $1 AND a.flow_id = $2 AND f.deleted_at IS NULL AND a.deleted_at IS NULL
`

type GetFlowAssistantParams struct {
	ID     int64 `json:"id"`
	FlowID int64 `json:"flow_id"`
}

func (q *Queries) GetFlowAssistant(ctx context.Context, arg GetFlowAssistantParams) (Assistant, error) {
	row := q.db.QueryRowContext(ctx, getFlowAssistant, arg.ID, arg.FlowID)
	var i Assistant
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Model,
		&i.ModelProvider,
		&i.Language,
		&i.Functions,
		&i.Prompts,
		&i.TraceID,
		&i.FlowID,
		&i.UseAgents,
		&i.MsgchainID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getFlowAssistants = `-- name: GetFlowAssistants :many
SELECT
  a.id, a.status, a.title, a.model, a.model_provider, a.language, a.functions, a.prompts, a.trace_id, a.flow_id, a.use_agents, a.msgchain_id, a.created_at, a.updated_at, a.deleted_at
FROM assistants a
INNER JOIN flows f ON a.flow_id = f.id
WHERE a.flow_id = $1 AND f.deleted_at IS NULL AND a.deleted_at IS NULL
ORDER BY a.created_at DESC
`

func (q *Queries) GetFlowAssistants(ctx context.Context, flowID int64) ([]Assistant, error) {
	rows, err := q.db.QueryContext(ctx, getFlowAssistants, flowID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Assistant
	for rows.Next() {
		var i Assistant
		if err := rows.Scan(
			&i.ID,
			&i.Status,
			&i.Title,
			&i.Model,
			&i.ModelProvider,
			&i.Language,
			&i.Functions,
			&i.Prompts,
			&i.TraceID,
			&i.FlowID,
			&i.UseAgents,
			&i.MsgchainID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserFlowAssistant = `-- name: GetUserFlowAssistant :one
SELECT
  a.id, a.status, a.title, a.model, a.model_provider, a.language, a.functions, a.prompts, a.trace_id, a.flow_id, a.use_agents, a.msgchain_id, a.created_at, a.updated_at, a.deleted_at
FROM assistants a
INNER JOIN flows f ON a.flow_id = f.id
INNER JOIN users u ON f.user_id = u.id
WHERE a.id = $1 AND a.flow_id = $2 AND f.user_id = $3 AND f.deleted_at IS NULL AND a.deleted_at IS NULL
`

type GetUserFlowAssistantParams struct {
	ID     int64 `json:"id"`
	FlowID int64 `json:"flow_id"`
	UserID int64 `json:"user_id"`
}

func (q *Queries) GetUserFlowAssistant(ctx context.Context, arg GetUserFlowAssistantParams) (Assistant, error) {
	row := q.db.QueryRowContext(ctx, getUserFlowAssistant, arg.ID, arg.FlowID, arg.UserID)
	var i Assistant
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Model,
		&i.ModelProvider,
		&i.Language,
		&i.Functions,
		&i.Prompts,
		&i.TraceID,
		&i.FlowID,
		&i.UseAgents,
		&i.MsgchainID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getUserFlowAssistants = `-- name: GetUserFlowAssistants :many
SELECT
  a.id, a.status, a.title, a.model, a.model_provider, a.language, a.functions, a.prompts, a.trace_id, a.flow_id, a.use_agents, a.msgchain_id, a.created_at, a.updated_at, a.deleted_at
FROM assistants a
INNER JOIN flows f ON a.flow_id = f.id
INNER JOIN users u ON f.user_id = u.id
WHERE a.flow_id = $1 AND f.user_id = $2 AND f.deleted_at IS NULL AND a.deleted_at IS NULL
ORDER BY a.created_at DESC
`

type GetUserFlowAssistantsParams struct {
	FlowID int64 `json:"flow_id"`
	UserID int64 `json:"user_id"`
}

func (q *Queries) GetUserFlowAssistants(ctx context.Context, arg GetUserFlowAssistantsParams) ([]Assistant, error) {
	rows, err := q.db.QueryContext(ctx, getUserFlowAssistants, arg.FlowID, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Assistant
	for rows.Next() {
		var i Assistant
		if err := rows.Scan(
			&i.ID,
			&i.Status,
			&i.Title,
			&i.Model,
			&i.ModelProvider,
			&i.Language,
			&i.Functions,
			&i.Prompts,
			&i.TraceID,
			&i.FlowID,
			&i.UseAgents,
			&i.MsgchainID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateAssistant = `-- name: UpdateAssistant :one
UPDATE assistants
SET title = $1, model = $2, language = $3, functions = $4, prompts = $5, trace_id = $6, msgchain_id = $7
WHERE id = $8
RETURNING id, status, title, model, model_provider, language, functions, prompts, trace_id, flow_id, use_agents, msgchain_id, created_at, updated_at, deleted_at
`

type UpdateAssistantParams struct {
	Title      string          `json:"title"`
	Model      string          `json:"model"`
	Language   string          `json:"language"`
	Functions  json.RawMessage `json:"functions"`
	Prompts    json.RawMessage `json:"prompts"`
	TraceID    sql.NullString  `json:"trace_id"`
	MsgchainID sql.NullInt64   `json:"msgchain_id"`
	ID         int64           `json:"id"`
}

func (q *Queries) UpdateAssistant(ctx context.Context, arg UpdateAssistantParams) (Assistant, error) {
	row := q.db.QueryRowContext(ctx, updateAssistant,
		arg.Title,
		arg.Model,
		arg.Language,
		arg.Functions,
		arg.Prompts,
		arg.TraceID,
		arg.MsgchainID,
		arg.ID,
	)
	var i Assistant
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Model,
		&i.ModelProvider,
		&i.Language,
		&i.Functions,
		&i.Prompts,
		&i.TraceID,
		&i.FlowID,
		&i.UseAgents,
		&i.MsgchainID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const updateAssistantLanguage = `-- name: UpdateAssistantLanguage :one
UPDATE assistants
SET language = $1
WHERE id = $2
RETURNING id, status, title, model, model_provider, language, functions, prompts, trace_id, flow_id, use_agents, msgchain_id, created_at, updated_at, deleted_at
`

type UpdateAssistantLanguageParams struct {
	Language string `json:"language"`
	ID       int64  `json:"id"`
}

func (q *Queries) UpdateAssistantLanguage(ctx context.Context, arg UpdateAssistantLanguageParams) (Assistant, error) {
	row := q.db.QueryRowContext(ctx, updateAssistantLanguage, arg.Language, arg.ID)
	var i Assistant
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Model,
		&i.ModelProvider,
		&i.Language,
		&i.Functions,
		&i.Prompts,
		&i.TraceID,
		&i.FlowID,
		&i.UseAgents,
		&i.MsgchainID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const updateAssistantModel = `-- name: UpdateAssistantModel :one
UPDATE assistants
SET model = $1
WHERE id = $2
RETURNING id, status, title, model, model_provider, language, functions, prompts, trace_id, flow_id, use_agents, msgchain_id, created_at, updated_at, deleted_at
`

type UpdateAssistantModelParams struct {
	Model string `json:"model"`
	ID    int64  `json:"id"`
}

func (q *Queries) UpdateAssistantModel(ctx context.Context, arg UpdateAssistantModelParams) (Assistant, error) {
	row := q.db.QueryRowContext(ctx, updateAssistantModel, arg.Model, arg.ID)
	var i Assistant
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Model,
		&i.ModelProvider,
		&i.Language,
		&i.Functions,
		&i.Prompts,
		&i.TraceID,
		&i.FlowID,
		&i.UseAgents,
		&i.MsgchainID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const updateAssistantStatus = `-- name: UpdateAssistantStatus :one
UPDATE assistants
SET status = $1
WHERE id = $2
RETURNING id, status, title, model, model_provider, language, functions, prompts, trace_id, flow_id, use_agents, msgchain_id, created_at, updated_at, deleted_at
`

type UpdateAssistantStatusParams struct {
	Status AssistantStatus `json:"status"`
	ID     int64           `json:"id"`
}

func (q *Queries) UpdateAssistantStatus(ctx context.Context, arg UpdateAssistantStatusParams) (Assistant, error) {
	row := q.db.QueryRowContext(ctx, updateAssistantStatus, arg.Status, arg.ID)
	var i Assistant
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Model,
		&i.ModelProvider,
		&i.Language,
		&i.Functions,
		&i.Prompts,
		&i.TraceID,
		&i.FlowID,
		&i.UseAgents,
		&i.MsgchainID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const updateAssistantTitle = `-- name: UpdateAssistantTitle :one
UPDATE assistants
SET title = $1
WHERE id = $2
RETURNING id, status, title, model, model_provider, language, functions, prompts, trace_id, flow_id, use_agents, msgchain_id, created_at, updated_at, deleted_at
`

type UpdateAssistantTitleParams struct {
	Title string `json:"title"`
	ID    int64  `json:"id"`
}

func (q *Queries) UpdateAssistantTitle(ctx context.Context, arg UpdateAssistantTitleParams) (Assistant, error) {
	row := q.db.QueryRowContext(ctx, updateAssistantTitle, arg.Title, arg.ID)
	var i Assistant
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Model,
		&i.ModelProvider,
		&i.Language,
		&i.Functions,
		&i.Prompts,
		&i.TraceID,
		&i.FlowID,
		&i.UseAgents,
		&i.MsgchainID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const updateAssistantUseAgents = `-- name: UpdateAssistantUseAgents :one
UPDATE assistants
SET use_agents = $1
WHERE id = $2
RETURNING id, status, title, model, model_provider, language, functions, prompts, trace_id, flow_id, use_agents, msgchain_id, created_at, updated_at, deleted_at
`

type UpdateAssistantUseAgentsParams struct {
	UseAgents bool  `json:"use_agents"`
	ID        int64 `json:"id"`
}

func (q *Queries) UpdateAssistantUseAgents(ctx context.Context, arg UpdateAssistantUseAgentsParams) (Assistant, error) {
	row := q.db.QueryRowContext(ctx, updateAssistantUseAgents, arg.UseAgents, arg.ID)
	var i Assistant
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Model,
		&i.ModelProvider,
		&i.Language,
		&i.Functions,
		&i.Prompts,
		&i.TraceID,
		&i.FlowID,
		&i.UseAgents,
		&i.MsgchainID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}
