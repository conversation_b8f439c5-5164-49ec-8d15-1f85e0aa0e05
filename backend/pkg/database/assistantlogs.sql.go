// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0
// source: assistantlogs.sql

package database

import (
	"context"
	"database/sql"
)

const createAssistantLog = `-- name: CreateAssistantLog :one
INSERT INTO assistantlogs (
  type,
  message,
  thinking,
  flow_id,
  assistant_id
)
VALUES (
  $1, $2, $3, $4, $5
)
RETURNING id, type, message, result, result_format, flow_id, assistant_id, created_at, thinking
`

type CreateAssistantLogParams struct {
	Type        MsglogType     `json:"type"`
	Message     string         `json:"message"`
	Thinking    sql.NullString `json:"thinking"`
	FlowID      int64          `json:"flow_id"`
	AssistantID int64          `json:"assistant_id"`
}

func (q *Queries) CreateAssistantLog(ctx context.Context, arg CreateAssistantLogParams) (Assistantlog, error) {
	row := q.db.QueryRowContext(ctx, createAssistantLog,
		arg.Type,
		arg.Message,
		arg.Thinking,
		arg.FlowID,
		arg.AssistantID,
	)
	var i Assistantlog
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Message,
		&i.Result,
		&i.ResultFormat,
		&i.FlowID,
		&i.AssistantID,
		&i.CreatedAt,
		&i.Thinking,
	)
	return i, err
}

const createResultAssistantLog = `-- name: CreateResultAssistantLog :one
INSERT INTO assistantlogs (
  type,
  message,
  thinking,
  result,
  result_format,
  flow_id,
  assistant_id
)
VALUES (
  $1, $2, $3, $4, $5, $6, $7
)
RETURNING id, type, message, result, result_format, flow_id, assistant_id, created_at, thinking
`

type CreateResultAssistantLogParams struct {
	Type         MsglogType         `json:"type"`
	Message      string             `json:"message"`
	Thinking     sql.NullString     `json:"thinking"`
	Result       string             `json:"result"`
	ResultFormat MsglogResultFormat `json:"result_format"`
	FlowID       int64              `json:"flow_id"`
	AssistantID  int64              `json:"assistant_id"`
}

func (q *Queries) CreateResultAssistantLog(ctx context.Context, arg CreateResultAssistantLogParams) (Assistantlog, error) {
	row := q.db.QueryRowContext(ctx, createResultAssistantLog,
		arg.Type,
		arg.Message,
		arg.Thinking,
		arg.Result,
		arg.ResultFormat,
		arg.FlowID,
		arg.AssistantID,
	)
	var i Assistantlog
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Message,
		&i.Result,
		&i.ResultFormat,
		&i.FlowID,
		&i.AssistantID,
		&i.CreatedAt,
		&i.Thinking,
	)
	return i, err
}

const getFlowAssistantLog = `-- name: GetFlowAssistantLog :one
SELECT
  al.id, al.type, al.message, al.result, al.result_format, al.flow_id, al.assistant_id, al.created_at, al.thinking
FROM assistantlogs al
INNER JOIN assistants a ON al.assistant_id = a.id
INNER JOIN flows f ON al.flow_id = f.id
WHERE al.id = $1 AND f.deleted_at IS NULL AND a.deleted_at IS NULL
`

func (q *Queries) GetFlowAssistantLog(ctx context.Context, id int64) (Assistantlog, error) {
	row := q.db.QueryRowContext(ctx, getFlowAssistantLog, id)
	var i Assistantlog
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Message,
		&i.Result,
		&i.ResultFormat,
		&i.FlowID,
		&i.AssistantID,
		&i.CreatedAt,
		&i.Thinking,
	)
	return i, err
}

const getFlowAssistantLogs = `-- name: GetFlowAssistantLogs :many
SELECT
  al.id, al.type, al.message, al.result, al.result_format, al.flow_id, al.assistant_id, al.created_at, al.thinking
FROM assistantlogs al
INNER JOIN assistants a ON al.assistant_id = a.id
INNER JOIN flows f ON al.flow_id = f.id
WHERE al.flow_id = $1 AND al.assistant_id = $2 AND f.deleted_at IS NULL AND a.deleted_at IS NULL
ORDER BY al.created_at ASC
`

type GetFlowAssistantLogsParams struct {
	FlowID      int64 `json:"flow_id"`
	AssistantID int64 `json:"assistant_id"`
}

func (q *Queries) GetFlowAssistantLogs(ctx context.Context, arg GetFlowAssistantLogsParams) ([]Assistantlog, error) {
	rows, err := q.db.QueryContext(ctx, getFlowAssistantLogs, arg.FlowID, arg.AssistantID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Assistantlog
	for rows.Next() {
		var i Assistantlog
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Message,
			&i.Result,
			&i.ResultFormat,
			&i.FlowID,
			&i.AssistantID,
			&i.CreatedAt,
			&i.Thinking,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserFlowAssistantLogs = `-- name: GetUserFlowAssistantLogs :many
SELECT
  al.id, al.type, al.message, al.result, al.result_format, al.flow_id, al.assistant_id, al.created_at, al.thinking
FROM assistantlogs al
INNER JOIN assistants a ON al.assistant_id = a.id
INNER JOIN flows f ON al.flow_id = f.id
INNER JOIN users u ON f.user_id = u.id
WHERE al.flow_id = $1 AND al.assistant_id = $2 AND f.user_id = $3 AND f.deleted_at IS NULL AND a.deleted_at IS NULL
ORDER BY al.created_at ASC
`

type GetUserFlowAssistantLogsParams struct {
	FlowID      int64 `json:"flow_id"`
	AssistantID int64 `json:"assistant_id"`
	UserID      int64 `json:"user_id"`
}

func (q *Queries) GetUserFlowAssistantLogs(ctx context.Context, arg GetUserFlowAssistantLogsParams) ([]Assistantlog, error) {
	rows, err := q.db.QueryContext(ctx, getUserFlowAssistantLogs, arg.FlowID, arg.AssistantID, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Assistantlog
	for rows.Next() {
		var i Assistantlog
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Message,
			&i.Result,
			&i.ResultFormat,
			&i.FlowID,
			&i.AssistantID,
			&i.CreatedAt,
			&i.Thinking,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateAssistantLog = `-- name: UpdateAssistantLog :one
UPDATE assistantlogs
SET type = $1, message = $2, thinking = $3, result = $4, result_format = $5
WHERE id = $6
RETURNING id, type, message, result, result_format, flow_id, assistant_id, created_at, thinking
`

type UpdateAssistantLogParams struct {
	Type         MsglogType         `json:"type"`
	Message      string             `json:"message"`
	Thinking     sql.NullString     `json:"thinking"`
	Result       string             `json:"result"`
	ResultFormat MsglogResultFormat `json:"result_format"`
	ID           int64              `json:"id"`
}

func (q *Queries) UpdateAssistantLog(ctx context.Context, arg UpdateAssistantLogParams) (Assistantlog, error) {
	row := q.db.QueryRowContext(ctx, updateAssistantLog,
		arg.Type,
		arg.Message,
		arg.Thinking,
		arg.Result,
		arg.ResultFormat,
		arg.ID,
	)
	var i Assistantlog
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Message,
		&i.Result,
		&i.ResultFormat,
		&i.FlowID,
		&i.AssistantID,
		&i.CreatedAt,
		&i.Thinking,
	)
	return i, err
}

const updateAssistantLogContent = `-- name: UpdateAssistantLogContent :one
UPDATE assistantlogs
SET type = $1, message = $2, thinking = $3
WHERE id = $4
RETURNING id, type, message, result, result_format, flow_id, assistant_id, created_at, thinking
`

type UpdateAssistantLogContentParams struct {
	Type     MsglogType     `json:"type"`
	Message  string         `json:"message"`
	Thinking sql.NullString `json:"thinking"`
	ID       int64          `json:"id"`
}

func (q *Queries) UpdateAssistantLogContent(ctx context.Context, arg UpdateAssistantLogContentParams) (Assistantlog, error) {
	row := q.db.QueryRowContext(ctx, updateAssistantLogContent,
		arg.Type,
		arg.Message,
		arg.Thinking,
		arg.ID,
	)
	var i Assistantlog
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Message,
		&i.Result,
		&i.ResultFormat,
		&i.FlowID,
		&i.AssistantID,
		&i.CreatedAt,
		&i.Thinking,
	)
	return i, err
}

const updateAssistantLogResult = `-- name: UpdateAssistantLogResult :one
UPDATE assistantlogs
SET result = $1, result_format = $2
WHERE id = $3
RETURNING id, type, message, result, result_format, flow_id, assistant_id, created_at, thinking
`

type UpdateAssistantLogResultParams struct {
	Result       string             `json:"result"`
	ResultFormat MsglogResultFormat `json:"result_format"`
	ID           int64              `json:"id"`
}

func (q *Queries) UpdateAssistantLogResult(ctx context.Context, arg UpdateAssistantLogResultParams) (Assistantlog, error) {
	row := q.db.QueryRowContext(ctx, updateAssistantLogResult, arg.Result, arg.ResultFormat, arg.ID)
	var i Assistantlog
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Message,
		&i.Result,
		&i.ResultFormat,
		&i.FlowID,
		&i.AssistantID,
		&i.CreatedAt,
		&i.Thinking,
	)
	return i, err
}
