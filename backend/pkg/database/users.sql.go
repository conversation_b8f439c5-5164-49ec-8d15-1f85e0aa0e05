// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0
// source: users.sql

package database

import (
	"context"
	"database/sql"

	"github.com/lib/pq"
)

const createUser = `-- name: CreateUser :one
INSERT INTO users (
  type,
  mail,
  name,
  password,
  status,
  role_id,
  password_change_required
)
VALUES (
  $1, $2, $3, $4, $5, $6, $7
)
RETURNING id, hash, type, mail, name, password, status, role_id, password_change_required, provider, created_at
`

type CreateUserParams struct {
	Type                   UserType       `json:"type"`
	Mail                   string         `json:"mail"`
	Name                   string         `json:"name"`
	Password               sql.NullString `json:"password"`
	Status                 UserStatus     `json:"status"`
	RoleID                 int64          `json:"role_id"`
	PasswordChangeRequired bool           `json:"password_change_required"`
}

func (q *Queries) CreateUser(ctx context.Context, arg CreateUserParams) (User, error) {
	row := q.db.QueryRowContext(ctx, createUser,
		arg.Type,
		arg.Mail,
		arg.Name,
		arg.Password,
		arg.Status,
		arg.RoleID,
		arg.PasswordChangeRequired,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Hash,
		&i.Type,
		&i.Mail,
		&i.Name,
		&i.Password,
		&i.Status,
		&i.RoleID,
		&i.PasswordChangeRequired,
		&i.Provider,
		&i.CreatedAt,
	)
	return i, err
}

const deleteUser = `-- name: DeleteUser :exec
DELETE FROM users
WHERE id = $1
`

func (q *Queries) DeleteUser(ctx context.Context, id int64) error {
	_, err := q.db.ExecContext(ctx, deleteUser, id)
	return err
}

const getUser = `-- name: GetUser :one
SELECT
  u.id, u.hash, u.type, u.mail, u.name, u.password, u.status, u.role_id, u.password_change_required, u.provider, u.created_at,
  r.name AS role_name,
  (
    SELECT ARRAY_AGG(p.name)
    FROM privileges p
    WHERE p.role_id = r.id
  ) AS privileges
FROM users u
INNER JOIN roles r ON u.role_id = r.id
WHERE u.id = $1
`

type GetUserRow struct {
	ID                     int64          `json:"id"`
	Hash                   string         `json:"hash"`
	Type                   UserType       `json:"type"`
	Mail                   string         `json:"mail"`
	Name                   string         `json:"name"`
	Password               sql.NullString `json:"password"`
	Status                 UserStatus     `json:"status"`
	RoleID                 int64          `json:"role_id"`
	PasswordChangeRequired bool           `json:"password_change_required"`
	Provider               sql.NullString `json:"provider"`
	CreatedAt              sql.NullTime   `json:"created_at"`
	RoleName               string         `json:"role_name"`
	Privileges             []string       `json:"privileges"`
}

func (q *Queries) GetUser(ctx context.Context, id int64) (GetUserRow, error) {
	row := q.db.QueryRowContext(ctx, getUser, id)
	var i GetUserRow
	err := row.Scan(
		&i.ID,
		&i.Hash,
		&i.Type,
		&i.Mail,
		&i.Name,
		&i.Password,
		&i.Status,
		&i.RoleID,
		&i.PasswordChangeRequired,
		&i.Provider,
		&i.CreatedAt,
		&i.RoleName,
		pq.Array(&i.Privileges),
	)
	return i, err
}

const getUserByHash = `-- name: GetUserByHash :one
SELECT
  u.id, u.hash, u.type, u.mail, u.name, u.password, u.status, u.role_id, u.password_change_required, u.provider, u.created_at,
  r.name AS role_name,
  (
    SELECT ARRAY_AGG(p.name)
    FROM privileges p
    WHERE p.role_id = r.id
  ) AS privileges
FROM users u
INNER JOIN roles r ON u.role_id = r.id
WHERE u.hash = $1
`

type GetUserByHashRow struct {
	ID                     int64          `json:"id"`
	Hash                   string         `json:"hash"`
	Type                   UserType       `json:"type"`
	Mail                   string         `json:"mail"`
	Name                   string         `json:"name"`
	Password               sql.NullString `json:"password"`
	Status                 UserStatus     `json:"status"`
	RoleID                 int64          `json:"role_id"`
	PasswordChangeRequired bool           `json:"password_change_required"`
	Provider               sql.NullString `json:"provider"`
	CreatedAt              sql.NullTime   `json:"created_at"`
	RoleName               string         `json:"role_name"`
	Privileges             []string       `json:"privileges"`
}

func (q *Queries) GetUserByHash(ctx context.Context, hash string) (GetUserByHashRow, error) {
	row := q.db.QueryRowContext(ctx, getUserByHash, hash)
	var i GetUserByHashRow
	err := row.Scan(
		&i.ID,
		&i.Hash,
		&i.Type,
		&i.Mail,
		&i.Name,
		&i.Password,
		&i.Status,
		&i.RoleID,
		&i.PasswordChangeRequired,
		&i.Provider,
		&i.CreatedAt,
		&i.RoleName,
		pq.Array(&i.Privileges),
	)
	return i, err
}

const getUsers = `-- name: GetUsers :many
SELECT
  u.id, u.hash, u.type, u.mail, u.name, u.password, u.status, u.role_id, u.password_change_required, u.provider, u.created_at,
  r.name AS role_name,
  (
    SELECT ARRAY_AGG(p.name)
    FROM privileges p
    WHERE p.role_id = r.id
  ) AS privileges
FROM users u
INNER JOIN roles r ON u.role_id = r.id
ORDER BY u.created_at DESC
`

type GetUsersRow struct {
	ID                     int64          `json:"id"`
	Hash                   string         `json:"hash"`
	Type                   UserType       `json:"type"`
	Mail                   string         `json:"mail"`
	Name                   string         `json:"name"`
	Password               sql.NullString `json:"password"`
	Status                 UserStatus     `json:"status"`
	RoleID                 int64          `json:"role_id"`
	PasswordChangeRequired bool           `json:"password_change_required"`
	Provider               sql.NullString `json:"provider"`
	CreatedAt              sql.NullTime   `json:"created_at"`
	RoleName               string         `json:"role_name"`
	Privileges             []string       `json:"privileges"`
}

func (q *Queries) GetUsers(ctx context.Context) ([]GetUsersRow, error) {
	rows, err := q.db.QueryContext(ctx, getUsers)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetUsersRow
	for rows.Next() {
		var i GetUsersRow
		if err := rows.Scan(
			&i.ID,
			&i.Hash,
			&i.Type,
			&i.Mail,
			&i.Name,
			&i.Password,
			&i.Status,
			&i.RoleID,
			&i.PasswordChangeRequired,
			&i.Provider,
			&i.CreatedAt,
			&i.RoleName,
			pq.Array(&i.Privileges),
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateUserName = `-- name: UpdateUserName :one
UPDATE users
SET name = $1
WHERE id = $2
RETURNING id, hash, type, mail, name, password, status, role_id, password_change_required, provider, created_at
`

type UpdateUserNameParams struct {
	Name string `json:"name"`
	ID   int64  `json:"id"`
}

func (q *Queries) UpdateUserName(ctx context.Context, arg UpdateUserNameParams) (User, error) {
	row := q.db.QueryRowContext(ctx, updateUserName, arg.Name, arg.ID)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Hash,
		&i.Type,
		&i.Mail,
		&i.Name,
		&i.Password,
		&i.Status,
		&i.RoleID,
		&i.PasswordChangeRequired,
		&i.Provider,
		&i.CreatedAt,
	)
	return i, err
}

const updateUserPassword = `-- name: UpdateUserPassword :one
UPDATE users
SET password = $1
WHERE id = $2
RETURNING id, hash, type, mail, name, password, status, role_id, password_change_required, provider, created_at
`

type UpdateUserPasswordParams struct {
	Password sql.NullString `json:"password"`
	ID       int64          `json:"id"`
}

func (q *Queries) UpdateUserPassword(ctx context.Context, arg UpdateUserPasswordParams) (User, error) {
	row := q.db.QueryRowContext(ctx, updateUserPassword, arg.Password, arg.ID)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Hash,
		&i.Type,
		&i.Mail,
		&i.Name,
		&i.Password,
		&i.Status,
		&i.RoleID,
		&i.PasswordChangeRequired,
		&i.Provider,
		&i.CreatedAt,
	)
	return i, err
}

const updateUserPasswordChangeRequired = `-- name: UpdateUserPasswordChangeRequired :one
UPDATE users
SET password_change_required = $1
WHERE id = $2
RETURNING id, hash, type, mail, name, password, status, role_id, password_change_required, provider, created_at
`

type UpdateUserPasswordChangeRequiredParams struct {
	PasswordChangeRequired bool  `json:"password_change_required"`
	ID                     int64 `json:"id"`
}

func (q *Queries) UpdateUserPasswordChangeRequired(ctx context.Context, arg UpdateUserPasswordChangeRequiredParams) (User, error) {
	row := q.db.QueryRowContext(ctx, updateUserPasswordChangeRequired, arg.PasswordChangeRequired, arg.ID)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Hash,
		&i.Type,
		&i.Mail,
		&i.Name,
		&i.Password,
		&i.Status,
		&i.RoleID,
		&i.PasswordChangeRequired,
		&i.Provider,
		&i.CreatedAt,
	)
	return i, err
}

const updateUserRole = `-- name: UpdateUserRole :one
UPDATE users
SET role_id = $1
WHERE id = $2
RETURNING id, hash, type, mail, name, password, status, role_id, password_change_required, provider, created_at
`

type UpdateUserRoleParams struct {
	RoleID int64 `json:"role_id"`
	ID     int64 `json:"id"`
}

func (q *Queries) UpdateUserRole(ctx context.Context, arg UpdateUserRoleParams) (User, error) {
	row := q.db.QueryRowContext(ctx, updateUserRole, arg.RoleID, arg.ID)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Hash,
		&i.Type,
		&i.Mail,
		&i.Name,
		&i.Password,
		&i.Status,
		&i.RoleID,
		&i.PasswordChangeRequired,
		&i.Provider,
		&i.CreatedAt,
	)
	return i, err
}

const updateUserStatus = `-- name: UpdateUserStatus :one
UPDATE users
SET status = $1
WHERE id = $2
RETURNING id, hash, type, mail, name, password, status, role_id, password_change_required, provider, created_at
`

type UpdateUserStatusParams struct {
	Status UserStatus `json:"status"`
	ID     int64      `json:"id"`
}

func (q *Queries) UpdateUserStatus(ctx context.Context, arg UpdateUserStatusParams) (User, error) {
	row := q.db.QueryRowContext(ctx, updateUserStatus, arg.Status, arg.ID)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Hash,
		&i.Type,
		&i.Mail,
		&i.Name,
		&i.Password,
		&i.Status,
		&i.RoleID,
		&i.PasswordChangeRequired,
		&i.Provider,
		&i.CreatedAt,
	)
	return i, err
}
