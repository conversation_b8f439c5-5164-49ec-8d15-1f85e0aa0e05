// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0
// source: agentlogs.sql

package database

import (
	"context"
	"database/sql"
)

const createAgentLog = `-- name: CreateAgentLog :one
INSERT INTO agentlogs (
  initiator,
  executor,
  task,
  result,
  flow_id,
  task_id,
  subtask_id
)
VALUES (
  $1, $2, $3, $4, $5, $6, $7
)
RETURNING id, initiator, executor, task, result, flow_id, task_id, subtask_id, created_at
`

type CreateAgentLogParams struct {
	Initiator MsgchainType  `json:"initiator"`
	Executor  MsgchainType  `json:"executor"`
	Task      string        `json:"task"`
	Result    string        `json:"result"`
	FlowID    int64         `json:"flow_id"`
	TaskID    sql.NullInt64 `json:"task_id"`
	SubtaskID sql.NullInt64 `json:"subtask_id"`
}

func (q *Queries) CreateAgentLog(ctx context.Context, arg CreateAgentLogParams) (Agentlog, error) {
	row := q.db.QueryRowContext(ctx, createAgentLog,
		arg.Initiator,
		arg.Executor,
		arg.Task,
		arg.Result,
		arg.FlowID,
		arg.TaskID,
		arg.SubtaskID,
	)
	var i Agentlog
	err := row.Scan(
		&i.ID,
		&i.Initiator,
		&i.Executor,
		&i.Task,
		&i.Result,
		&i.FlowID,
		&i.TaskID,
		&i.SubtaskID,
		&i.CreatedAt,
	)
	return i, err
}

const getFlowAgentLog = `-- name: GetFlowAgentLog :one
SELECT
  al.id, al.initiator, al.executor, al.task, al.result, al.flow_id, al.task_id, al.subtask_id, al.created_at
FROM agentlogs al
INNER JOIN flows f ON al.flow_id = f.id
WHERE al.id = $1 AND al.flow_id = $2 AND f.deleted_at IS NULL
`

type GetFlowAgentLogParams struct {
	ID     int64 `json:"id"`
	FlowID int64 `json:"flow_id"`
}

func (q *Queries) GetFlowAgentLog(ctx context.Context, arg GetFlowAgentLogParams) (Agentlog, error) {
	row := q.db.QueryRowContext(ctx, getFlowAgentLog, arg.ID, arg.FlowID)
	var i Agentlog
	err := row.Scan(
		&i.ID,
		&i.Initiator,
		&i.Executor,
		&i.Task,
		&i.Result,
		&i.FlowID,
		&i.TaskID,
		&i.SubtaskID,
		&i.CreatedAt,
	)
	return i, err
}

const getFlowAgentLogs = `-- name: GetFlowAgentLogs :many
SELECT
  al.id, al.initiator, al.executor, al.task, al.result, al.flow_id, al.task_id, al.subtask_id, al.created_at
FROM agentlogs al
INNER JOIN flows f ON al.flow_id = f.id
WHERE al.flow_id = $1 AND f.deleted_at IS NULL
ORDER BY al.created_at ASC
`

func (q *Queries) GetFlowAgentLogs(ctx context.Context, flowID int64) ([]Agentlog, error) {
	rows, err := q.db.QueryContext(ctx, getFlowAgentLogs, flowID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Agentlog
	for rows.Next() {
		var i Agentlog
		if err := rows.Scan(
			&i.ID,
			&i.Initiator,
			&i.Executor,
			&i.Task,
			&i.Result,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSubtaskAgentLogs = `-- name: GetSubtaskAgentLogs :many
SELECT
  al.id, al.initiator, al.executor, al.task, al.result, al.flow_id, al.task_id, al.subtask_id, al.created_at
FROM agentlogs al
INNER JOIN flows f ON al.flow_id = f.id
INNER JOIN subtasks s ON al.subtask_id = s.id
WHERE al.subtask_id = $1 AND f.deleted_at IS NULL
ORDER BY al.created_at ASC
`

func (q *Queries) GetSubtaskAgentLogs(ctx context.Context, subtaskID sql.NullInt64) ([]Agentlog, error) {
	rows, err := q.db.QueryContext(ctx, getSubtaskAgentLogs, subtaskID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Agentlog
	for rows.Next() {
		var i Agentlog
		if err := rows.Scan(
			&i.ID,
			&i.Initiator,
			&i.Executor,
			&i.Task,
			&i.Result,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTaskAgentLogs = `-- name: GetTaskAgentLogs :many
SELECT
  al.id, al.initiator, al.executor, al.task, al.result, al.flow_id, al.task_id, al.subtask_id, al.created_at
FROM agentlogs al
INNER JOIN flows f ON al.flow_id = f.id
INNER JOIN tasks t ON al.task_id = t.id
WHERE al.task_id = $1 AND f.deleted_at IS NULL
ORDER BY al.created_at ASC
`

func (q *Queries) GetTaskAgentLogs(ctx context.Context, taskID sql.NullInt64) ([]Agentlog, error) {
	rows, err := q.db.QueryContext(ctx, getTaskAgentLogs, taskID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Agentlog
	for rows.Next() {
		var i Agentlog
		if err := rows.Scan(
			&i.ID,
			&i.Initiator,
			&i.Executor,
			&i.Task,
			&i.Result,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserFlowAgentLogs = `-- name: GetUserFlowAgentLogs :many
SELECT
  al.id, al.initiator, al.executor, al.task, al.result, al.flow_id, al.task_id, al.subtask_id, al.created_at
FROM agentlogs al
INNER JOIN flows f ON al.flow_id = f.id
INNER JOIN users u ON f.user_id = u.id
WHERE al.flow_id = $1 AND f.user_id = $2 AND f.deleted_at IS NULL
ORDER BY al.created_at ASC
`

type GetUserFlowAgentLogsParams struct {
	FlowID int64 `json:"flow_id"`
	UserID int64 `json:"user_id"`
}

func (q *Queries) GetUserFlowAgentLogs(ctx context.Context, arg GetUserFlowAgentLogsParams) ([]Agentlog, error) {
	rows, err := q.db.QueryContext(ctx, getUserFlowAgentLogs, arg.FlowID, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Agentlog
	for rows.Next() {
		var i Agentlog
		if err := rows.Scan(
			&i.ID,
			&i.Initiator,
			&i.Executor,
			&i.Task,
			&i.Result,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
