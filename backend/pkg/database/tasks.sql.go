// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0
// source: tasks.sql

package database

import (
	"context"
)

const createTask = `-- name: CreateTask :one
INSERT INTO tasks (
  status,
  title,
  input,
  flow_id
) VALUES (
  $1, $2, $3, $4
)
RETURNING id, status, title, input, result, flow_id, created_at, updated_at
`

type CreateTaskParams struct {
	Status TaskStatus `json:"status"`
	Title  string     `json:"title"`
	Input  string     `json:"input"`
	FlowID int64      `json:"flow_id"`
}

func (q *Queries) CreateTask(ctx context.Context, arg CreateTaskParams) (Task, error) {
	row := q.db.QueryRowContext(ctx, createTask,
		arg.Status,
		arg.Title,
		arg.Input,
		arg.FlowID,
	)
	var i Task
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Input,
		&i.Result,
		&i.FlowID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getFlowTask = `-- name: GetFlowTask :one
SELECT
  t.id, t.status, t.title, t.input, t.result, t.flow_id, t.created_at, t.updated_at
FROM tasks t
INNER JOIN flows f ON t.flow_id = f.id
WHERE t.id = $1 AND t.flow_id = $2 AND f.deleted_at IS NULL
`

type GetFlowTaskParams struct {
	ID     int64 `json:"id"`
	FlowID int64 `json:"flow_id"`
}

func (q *Queries) GetFlowTask(ctx context.Context, arg GetFlowTaskParams) (Task, error) {
	row := q.db.QueryRowContext(ctx, getFlowTask, arg.ID, arg.FlowID)
	var i Task
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Input,
		&i.Result,
		&i.FlowID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getFlowTasks = `-- name: GetFlowTasks :many
SELECT
  t.id, t.status, t.title, t.input, t.result, t.flow_id, t.created_at, t.updated_at
FROM tasks t
INNER JOIN flows f ON t.flow_id = f.id
WHERE t.flow_id = $1 AND f.deleted_at IS NULL
ORDER BY t.created_at ASC
`

func (q *Queries) GetFlowTasks(ctx context.Context, flowID int64) ([]Task, error) {
	rows, err := q.db.QueryContext(ctx, getFlowTasks, flowID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Task
	for rows.Next() {
		var i Task
		if err := rows.Scan(
			&i.ID,
			&i.Status,
			&i.Title,
			&i.Input,
			&i.Result,
			&i.FlowID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTask = `-- name: GetTask :one
SELECT
  t.id, t.status, t.title, t.input, t.result, t.flow_id, t.created_at, t.updated_at
FROM tasks t
WHERE t.id = $1
`

func (q *Queries) GetTask(ctx context.Context, id int64) (Task, error) {
	row := q.db.QueryRowContext(ctx, getTask, id)
	var i Task
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Input,
		&i.Result,
		&i.FlowID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getUserFlowTask = `-- name: GetUserFlowTask :one
SELECT
  t.id, t.status, t.title, t.input, t.result, t.flow_id, t.created_at, t.updated_at
FROM tasks t
INNER JOIN flows f ON t.flow_id = f.id
INNER JOIN users u ON f.user_id = u.id
WHERE t.id = $1 AND t.flow_id = $2 AND f.user_id = $3 AND f.deleted_at IS NULL
`

type GetUserFlowTaskParams struct {
	ID     int64 `json:"id"`
	FlowID int64 `json:"flow_id"`
	UserID int64 `json:"user_id"`
}

func (q *Queries) GetUserFlowTask(ctx context.Context, arg GetUserFlowTaskParams) (Task, error) {
	row := q.db.QueryRowContext(ctx, getUserFlowTask, arg.ID, arg.FlowID, arg.UserID)
	var i Task
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Input,
		&i.Result,
		&i.FlowID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getUserFlowTasks = `-- name: GetUserFlowTasks :many
SELECT
  t.id, t.status, t.title, t.input, t.result, t.flow_id, t.created_at, t.updated_at
FROM tasks t
INNER JOIN flows f ON t.flow_id = f.id
INNER JOIN users u ON f.user_id = u.id
WHERE t.flow_id = $1 AND f.user_id = $2 AND f.deleted_at IS NULL
ORDER BY t.created_at ASC
`

type GetUserFlowTasksParams struct {
	FlowID int64 `json:"flow_id"`
	UserID int64 `json:"user_id"`
}

func (q *Queries) GetUserFlowTasks(ctx context.Context, arg GetUserFlowTasksParams) ([]Task, error) {
	rows, err := q.db.QueryContext(ctx, getUserFlowTasks, arg.FlowID, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Task
	for rows.Next() {
		var i Task
		if err := rows.Scan(
			&i.ID,
			&i.Status,
			&i.Title,
			&i.Input,
			&i.Result,
			&i.FlowID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateTaskFailedResult = `-- name: UpdateTaskFailedResult :one
UPDATE tasks
SET status = 'failed', result = $1
WHERE id = $2
RETURNING id, status, title, input, result, flow_id, created_at, updated_at
`

type UpdateTaskFailedResultParams struct {
	Result string `json:"result"`
	ID     int64  `json:"id"`
}

func (q *Queries) UpdateTaskFailedResult(ctx context.Context, arg UpdateTaskFailedResultParams) (Task, error) {
	row := q.db.QueryRowContext(ctx, updateTaskFailedResult, arg.Result, arg.ID)
	var i Task
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Input,
		&i.Result,
		&i.FlowID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateTaskFinishedResult = `-- name: UpdateTaskFinishedResult :one
UPDATE tasks
SET status = 'finished', result = $1
WHERE id = $2
RETURNING id, status, title, input, result, flow_id, created_at, updated_at
`

type UpdateTaskFinishedResultParams struct {
	Result string `json:"result"`
	ID     int64  `json:"id"`
}

func (q *Queries) UpdateTaskFinishedResult(ctx context.Context, arg UpdateTaskFinishedResultParams) (Task, error) {
	row := q.db.QueryRowContext(ctx, updateTaskFinishedResult, arg.Result, arg.ID)
	var i Task
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Input,
		&i.Result,
		&i.FlowID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateTaskResult = `-- name: UpdateTaskResult :one
UPDATE tasks
SET result = $1
WHERE id = $2
RETURNING id, status, title, input, result, flow_id, created_at, updated_at
`

type UpdateTaskResultParams struct {
	Result string `json:"result"`
	ID     int64  `json:"id"`
}

func (q *Queries) UpdateTaskResult(ctx context.Context, arg UpdateTaskResultParams) (Task, error) {
	row := q.db.QueryRowContext(ctx, updateTaskResult, arg.Result, arg.ID)
	var i Task
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Input,
		&i.Result,
		&i.FlowID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateTaskStatus = `-- name: UpdateTaskStatus :one
UPDATE tasks
SET status = $1
WHERE id = $2
RETURNING id, status, title, input, result, flow_id, created_at, updated_at
`

type UpdateTaskStatusParams struct {
	Status TaskStatus `json:"status"`
	ID     int64      `json:"id"`
}

func (q *Queries) UpdateTaskStatus(ctx context.Context, arg UpdateTaskStatusParams) (Task, error) {
	row := q.db.QueryRowContext(ctx, updateTaskStatus, arg.Status, arg.ID)
	var i Task
	err := row.Scan(
		&i.ID,
		&i.Status,
		&i.Title,
		&i.Input,
		&i.Result,
		&i.FlowID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
