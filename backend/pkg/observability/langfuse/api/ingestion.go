// This file was auto-generated by <PERSON>rn from our API Definition.

package api

import (
	json "encoding/json"
	fmt "fmt"
	internal "pentagi/pkg/observability/langfuse/api/internal"
	time "time"
)

type IngestionBatchRequest struct {
	// Batch of tracing events to be ingested. Discriminated by attribute `type`.
	Batch    []*IngestionEvent `json:"batch,omitempty" url:"-"`
	Metadata interface{}       `json:"metadata,omitempty" url:"-"`
}

type BaseEvent struct {
	// UUID v4 that identifies the event
	Id string `json:"id" url:"id"`
	// Datetime (ISO 8601) of event creation in client. Should be as close to actual event creation in client as possible, this timestamp will be used for ordering of events in future release. Resolution: milliseconds (required), microseconds (optimal).
	Timestamp string      `json:"timestamp" url:"timestamp"`
	Metadata  interface{} `json:"metadata,omitempty" url:"metadata,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (b *BaseEvent) GetId() string {
	if b == nil {
		return ""
	}
	return b.Id
}

func (b *BaseEvent) GetTimestamp() string {
	if b == nil {
		return ""
	}
	return b.Timestamp
}

func (b *BaseEvent) GetMetadata() interface{} {
	if b == nil {
		return nil
	}
	return b.Metadata
}

func (b *BaseEvent) GetExtraProperties() map[string]interface{} {
	return b.extraProperties
}

func (b *BaseEvent) UnmarshalJSON(data []byte) error {
	type unmarshaler BaseEvent
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*b = BaseEvent(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *b)
	if err != nil {
		return err
	}
	b.extraProperties = extraProperties
	b.rawJSON = json.RawMessage(data)
	return nil
}

func (b *BaseEvent) String() string {
	if len(b.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(b.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(b); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", b)
}

type CreateEventBody struct {
	TraceId             *string           `json:"traceId,omitempty" url:"traceId,omitempty"`
	Name                *string           `json:"name,omitempty" url:"name,omitempty"`
	StartTime           *time.Time        `json:"startTime,omitempty" url:"startTime,omitempty"`
	Metadata            interface{}       `json:"metadata,omitempty" url:"metadata,omitempty"`
	Input               interface{}       `json:"input,omitempty" url:"input,omitempty"`
	Output              interface{}       `json:"output,omitempty" url:"output,omitempty"`
	Level               *ObservationLevel `json:"level,omitempty" url:"level,omitempty"`
	StatusMessage       *string           `json:"statusMessage,omitempty" url:"statusMessage,omitempty"`
	ParentObservationId *string           `json:"parentObservationId,omitempty" url:"parentObservationId,omitempty"`
	Version             *string           `json:"version,omitempty" url:"version,omitempty"`
	Id                  *string           `json:"id,omitempty" url:"id,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (c *CreateEventBody) GetTraceId() *string {
	if c == nil {
		return nil
	}
	return c.TraceId
}

func (c *CreateEventBody) GetName() *string {
	if c == nil {
		return nil
	}
	return c.Name
}

func (c *CreateEventBody) GetStartTime() *time.Time {
	if c == nil {
		return nil
	}
	return c.StartTime
}

func (c *CreateEventBody) GetMetadata() interface{} {
	if c == nil {
		return nil
	}
	return c.Metadata
}

func (c *CreateEventBody) GetInput() interface{} {
	if c == nil {
		return nil
	}
	return c.Input
}

func (c *CreateEventBody) GetOutput() interface{} {
	if c == nil {
		return nil
	}
	return c.Output
}

func (c *CreateEventBody) GetLevel() *ObservationLevel {
	if c == nil {
		return nil
	}
	return c.Level
}

func (c *CreateEventBody) GetStatusMessage() *string {
	if c == nil {
		return nil
	}
	return c.StatusMessage
}

func (c *CreateEventBody) GetParentObservationId() *string {
	if c == nil {
		return nil
	}
	return c.ParentObservationId
}

func (c *CreateEventBody) GetVersion() *string {
	if c == nil {
		return nil
	}
	return c.Version
}

func (c *CreateEventBody) GetId() *string {
	if c == nil {
		return nil
	}
	return c.Id
}

func (c *CreateEventBody) GetExtraProperties() map[string]interface{} {
	return c.extraProperties
}

func (c *CreateEventBody) UnmarshalJSON(data []byte) error {
	type embed CreateEventBody
	var unmarshaler = struct {
		embed
		StartTime *internal.DateTime `json:"startTime,omitempty"`
	}{
		embed: embed(*c),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*c = CreateEventBody(unmarshaler.embed)
	c.StartTime = unmarshaler.StartTime.TimePtr()
	extraProperties, err := internal.ExtractExtraProperties(data, *c)
	if err != nil {
		return err
	}
	c.extraProperties = extraProperties
	c.rawJSON = json.RawMessage(data)
	return nil
}

func (c *CreateEventBody) MarshalJSON() ([]byte, error) {
	type embed CreateEventBody
	var marshaler = struct {
		embed
		StartTime *internal.DateTime `json:"startTime,omitempty"`
	}{
		embed:     embed(*c),
		StartTime: internal.NewOptionalDateTime(c.StartTime),
	}
	return json.Marshal(marshaler)
}

func (c *CreateEventBody) String() string {
	if len(c.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(c.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(c); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", c)
}

type CreateEventEvent struct {
	// UUID v4 that identifies the event
	Id string `json:"id" url:"id"`
	// Datetime (ISO 8601) of event creation in client. Should be as close to actual event creation in client as possible, this timestamp will be used for ordering of events in future release. Resolution: milliseconds (required), microseconds (optimal).
	Timestamp string           `json:"timestamp" url:"timestamp"`
	Metadata  interface{}      `json:"metadata,omitempty" url:"metadata,omitempty"`
	Body      *CreateEventBody `json:"body,omitempty" url:"body,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (c *CreateEventEvent) GetId() string {
	if c == nil {
		return ""
	}
	return c.Id
}

func (c *CreateEventEvent) GetTimestamp() string {
	if c == nil {
		return ""
	}
	return c.Timestamp
}

func (c *CreateEventEvent) GetMetadata() interface{} {
	if c == nil {
		return nil
	}
	return c.Metadata
}

func (c *CreateEventEvent) GetBody() *CreateEventBody {
	if c == nil {
		return nil
	}
	return c.Body
}

func (c *CreateEventEvent) GetExtraProperties() map[string]interface{} {
	return c.extraProperties
}

func (c *CreateEventEvent) UnmarshalJSON(data []byte) error {
	type unmarshaler CreateEventEvent
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*c = CreateEventEvent(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *c)
	if err != nil {
		return err
	}
	c.extraProperties = extraProperties
	c.rawJSON = json.RawMessage(data)
	return nil
}

func (c *CreateEventEvent) String() string {
	if len(c.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(c.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(c); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", c)
}

type CreateGenerationBody struct {
	TraceId             *string              `json:"traceId,omitempty" url:"traceId,omitempty"`
	Name                *string              `json:"name,omitempty" url:"name,omitempty"`
	StartTime           *time.Time           `json:"startTime,omitempty" url:"startTime,omitempty"`
	Metadata            interface{}          `json:"metadata,omitempty" url:"metadata,omitempty"`
	Input               interface{}          `json:"input,omitempty" url:"input,omitempty"`
	Output              interface{}          `json:"output,omitempty" url:"output,omitempty"`
	Level               *ObservationLevel    `json:"level,omitempty" url:"level,omitempty"`
	StatusMessage       *string              `json:"statusMessage,omitempty" url:"statusMessage,omitempty"`
	ParentObservationId *string              `json:"parentObservationId,omitempty" url:"parentObservationId,omitempty"`
	Version             *string              `json:"version,omitempty" url:"version,omitempty"`
	Id                  *string              `json:"id,omitempty" url:"id,omitempty"`
	EndTime             *time.Time           `json:"endTime,omitempty" url:"endTime,omitempty"`
	CompletionStartTime *time.Time           `json:"completionStartTime,omitempty" url:"completionStartTime,omitempty"`
	Model               *string              `json:"model,omitempty" url:"model,omitempty"`
	ModelParameters     map[string]*MapValue `json:"modelParameters,omitempty" url:"modelParameters,omitempty"`
	Usage               *IngestionUsage      `json:"usage,omitempty" url:"usage,omitempty"`
	PromptName          *string              `json:"promptName,omitempty" url:"promptName,omitempty"`
	PromptVersion       *int                 `json:"promptVersion,omitempty" url:"promptVersion,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (c *CreateGenerationBody) GetTraceId() *string {
	if c == nil {
		return nil
	}
	return c.TraceId
}

func (c *CreateGenerationBody) GetName() *string {
	if c == nil {
		return nil
	}
	return c.Name
}

func (c *CreateGenerationBody) GetStartTime() *time.Time {
	if c == nil {
		return nil
	}
	return c.StartTime
}

func (c *CreateGenerationBody) GetMetadata() interface{} {
	if c == nil {
		return nil
	}
	return c.Metadata
}

func (c *CreateGenerationBody) GetInput() interface{} {
	if c == nil {
		return nil
	}
	return c.Input
}

func (c *CreateGenerationBody) GetOutput() interface{} {
	if c == nil {
		return nil
	}
	return c.Output
}

func (c *CreateGenerationBody) GetLevel() *ObservationLevel {
	if c == nil {
		return nil
	}
	return c.Level
}

func (c *CreateGenerationBody) GetStatusMessage() *string {
	if c == nil {
		return nil
	}
	return c.StatusMessage
}

func (c *CreateGenerationBody) GetParentObservationId() *string {
	if c == nil {
		return nil
	}
	return c.ParentObservationId
}

func (c *CreateGenerationBody) GetVersion() *string {
	if c == nil {
		return nil
	}
	return c.Version
}

func (c *CreateGenerationBody) GetId() *string {
	if c == nil {
		return nil
	}
	return c.Id
}

func (c *CreateGenerationBody) GetEndTime() *time.Time {
	if c == nil {
		return nil
	}
	return c.EndTime
}

func (c *CreateGenerationBody) GetCompletionStartTime() *time.Time {
	if c == nil {
		return nil
	}
	return c.CompletionStartTime
}

func (c *CreateGenerationBody) GetModel() *string {
	if c == nil {
		return nil
	}
	return c.Model
}

func (c *CreateGenerationBody) GetModelParameters() map[string]*MapValue {
	if c == nil {
		return nil
	}
	return c.ModelParameters
}

func (c *CreateGenerationBody) GetUsage() *IngestionUsage {
	if c == nil {
		return nil
	}
	return c.Usage
}

func (c *CreateGenerationBody) GetPromptName() *string {
	if c == nil {
		return nil
	}
	return c.PromptName
}

func (c *CreateGenerationBody) GetPromptVersion() *int {
	if c == nil {
		return nil
	}
	return c.PromptVersion
}

func (c *CreateGenerationBody) GetExtraProperties() map[string]interface{} {
	return c.extraProperties
}

func (c *CreateGenerationBody) UnmarshalJSON(data []byte) error {
	type embed CreateGenerationBody
	var unmarshaler = struct {
		embed
		StartTime           *internal.DateTime `json:"startTime,omitempty"`
		EndTime             *internal.DateTime `json:"endTime,omitempty"`
		CompletionStartTime *internal.DateTime `json:"completionStartTime,omitempty"`
	}{
		embed: embed(*c),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*c = CreateGenerationBody(unmarshaler.embed)
	c.StartTime = unmarshaler.StartTime.TimePtr()
	c.EndTime = unmarshaler.EndTime.TimePtr()
	c.CompletionStartTime = unmarshaler.CompletionStartTime.TimePtr()
	extraProperties, err := internal.ExtractExtraProperties(data, *c)
	if err != nil {
		return err
	}
	c.extraProperties = extraProperties
	c.rawJSON = json.RawMessage(data)
	return nil
}

func (c *CreateGenerationBody) MarshalJSON() ([]byte, error) {
	type embed CreateGenerationBody
	var marshaler = struct {
		embed
		StartTime           *internal.DateTime `json:"startTime,omitempty"`
		EndTime             *internal.DateTime `json:"endTime,omitempty"`
		CompletionStartTime *internal.DateTime `json:"completionStartTime,omitempty"`
	}{
		embed:               embed(*c),
		StartTime:           internal.NewOptionalDateTime(c.StartTime),
		EndTime:             internal.NewOptionalDateTime(c.EndTime),
		CompletionStartTime: internal.NewOptionalDateTime(c.CompletionStartTime),
	}
	return json.Marshal(marshaler)
}

func (c *CreateGenerationBody) String() string {
	if len(c.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(c.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(c); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", c)
}

type CreateGenerationEvent struct {
	// UUID v4 that identifies the event
	Id string `json:"id" url:"id"`
	// Datetime (ISO 8601) of event creation in client. Should be as close to actual event creation in client as possible, this timestamp will be used for ordering of events in future release. Resolution: milliseconds (required), microseconds (optimal).
	Timestamp string                `json:"timestamp" url:"timestamp"`
	Metadata  interface{}           `json:"metadata,omitempty" url:"metadata,omitempty"`
	Body      *CreateGenerationBody `json:"body,omitempty" url:"body,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (c *CreateGenerationEvent) GetId() string {
	if c == nil {
		return ""
	}
	return c.Id
}

func (c *CreateGenerationEvent) GetTimestamp() string {
	if c == nil {
		return ""
	}
	return c.Timestamp
}

func (c *CreateGenerationEvent) GetMetadata() interface{} {
	if c == nil {
		return nil
	}
	return c.Metadata
}

func (c *CreateGenerationEvent) GetBody() *CreateGenerationBody {
	if c == nil {
		return nil
	}
	return c.Body
}

func (c *CreateGenerationEvent) GetExtraProperties() map[string]interface{} {
	return c.extraProperties
}

func (c *CreateGenerationEvent) UnmarshalJSON(data []byte) error {
	type unmarshaler CreateGenerationEvent
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*c = CreateGenerationEvent(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *c)
	if err != nil {
		return err
	}
	c.extraProperties = extraProperties
	c.rawJSON = json.RawMessage(data)
	return nil
}

func (c *CreateGenerationEvent) String() string {
	if len(c.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(c.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(c); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", c)
}

type CreateObservationEvent struct {
	// UUID v4 that identifies the event
	Id string `json:"id" url:"id"`
	// Datetime (ISO 8601) of event creation in client. Should be as close to actual event creation in client as possible, this timestamp will be used for ordering of events in future release. Resolution: milliseconds (required), microseconds (optimal).
	Timestamp string           `json:"timestamp" url:"timestamp"`
	Metadata  interface{}      `json:"metadata,omitempty" url:"metadata,omitempty"`
	Body      *ObservationBody `json:"body,omitempty" url:"body,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (c *CreateObservationEvent) GetId() string {
	if c == nil {
		return ""
	}
	return c.Id
}

func (c *CreateObservationEvent) GetTimestamp() string {
	if c == nil {
		return ""
	}
	return c.Timestamp
}

func (c *CreateObservationEvent) GetMetadata() interface{} {
	if c == nil {
		return nil
	}
	return c.Metadata
}

func (c *CreateObservationEvent) GetBody() *ObservationBody {
	if c == nil {
		return nil
	}
	return c.Body
}

func (c *CreateObservationEvent) GetExtraProperties() map[string]interface{} {
	return c.extraProperties
}

func (c *CreateObservationEvent) UnmarshalJSON(data []byte) error {
	type unmarshaler CreateObservationEvent
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*c = CreateObservationEvent(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *c)
	if err != nil {
		return err
	}
	c.extraProperties = extraProperties
	c.rawJSON = json.RawMessage(data)
	return nil
}

func (c *CreateObservationEvent) String() string {
	if len(c.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(c.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(c); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", c)
}

type CreateSpanBody struct {
	TraceId             *string           `json:"traceId,omitempty" url:"traceId,omitempty"`
	Name                *string           `json:"name,omitempty" url:"name,omitempty"`
	StartTime           *time.Time        `json:"startTime,omitempty" url:"startTime,omitempty"`
	Metadata            interface{}       `json:"metadata,omitempty" url:"metadata,omitempty"`
	Input               interface{}       `json:"input,omitempty" url:"input,omitempty"`
	Output              interface{}       `json:"output,omitempty" url:"output,omitempty"`
	Level               *ObservationLevel `json:"level,omitempty" url:"level,omitempty"`
	StatusMessage       *string           `json:"statusMessage,omitempty" url:"statusMessage,omitempty"`
	ParentObservationId *string           `json:"parentObservationId,omitempty" url:"parentObservationId,omitempty"`
	Version             *string           `json:"version,omitempty" url:"version,omitempty"`
	Id                  *string           `json:"id,omitempty" url:"id,omitempty"`
	EndTime             *time.Time        `json:"endTime,omitempty" url:"endTime,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (c *CreateSpanBody) GetTraceId() *string {
	if c == nil {
		return nil
	}
	return c.TraceId
}

func (c *CreateSpanBody) GetName() *string {
	if c == nil {
		return nil
	}
	return c.Name
}

func (c *CreateSpanBody) GetStartTime() *time.Time {
	if c == nil {
		return nil
	}
	return c.StartTime
}

func (c *CreateSpanBody) GetMetadata() interface{} {
	if c == nil {
		return nil
	}
	return c.Metadata
}

func (c *CreateSpanBody) GetInput() interface{} {
	if c == nil {
		return nil
	}
	return c.Input
}

func (c *CreateSpanBody) GetOutput() interface{} {
	if c == nil {
		return nil
	}
	return c.Output
}

func (c *CreateSpanBody) GetLevel() *ObservationLevel {
	if c == nil {
		return nil
	}
	return c.Level
}

func (c *CreateSpanBody) GetStatusMessage() *string {
	if c == nil {
		return nil
	}
	return c.StatusMessage
}

func (c *CreateSpanBody) GetParentObservationId() *string {
	if c == nil {
		return nil
	}
	return c.ParentObservationId
}

func (c *CreateSpanBody) GetVersion() *string {
	if c == nil {
		return nil
	}
	return c.Version
}

func (c *CreateSpanBody) GetId() *string {
	if c == nil {
		return nil
	}
	return c.Id
}

func (c *CreateSpanBody) GetEndTime() *time.Time {
	if c == nil {
		return nil
	}
	return c.EndTime
}

func (c *CreateSpanBody) GetExtraProperties() map[string]interface{} {
	return c.extraProperties
}

func (c *CreateSpanBody) UnmarshalJSON(data []byte) error {
	type embed CreateSpanBody
	var unmarshaler = struct {
		embed
		StartTime *internal.DateTime `json:"startTime,omitempty"`
		EndTime   *internal.DateTime `json:"endTime,omitempty"`
	}{
		embed: embed(*c),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*c = CreateSpanBody(unmarshaler.embed)
	c.StartTime = unmarshaler.StartTime.TimePtr()
	c.EndTime = unmarshaler.EndTime.TimePtr()
	extraProperties, err := internal.ExtractExtraProperties(data, *c)
	if err != nil {
		return err
	}
	c.extraProperties = extraProperties
	c.rawJSON = json.RawMessage(data)
	return nil
}

func (c *CreateSpanBody) MarshalJSON() ([]byte, error) {
	type embed CreateSpanBody
	var marshaler = struct {
		embed
		StartTime *internal.DateTime `json:"startTime,omitempty"`
		EndTime   *internal.DateTime `json:"endTime,omitempty"`
	}{
		embed:     embed(*c),
		StartTime: internal.NewOptionalDateTime(c.StartTime),
		EndTime:   internal.NewOptionalDateTime(c.EndTime),
	}
	return json.Marshal(marshaler)
}

func (c *CreateSpanBody) String() string {
	if len(c.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(c.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(c); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", c)
}

type CreateSpanEvent struct {
	// UUID v4 that identifies the event
	Id string `json:"id" url:"id"`
	// Datetime (ISO 8601) of event creation in client. Should be as close to actual event creation in client as possible, this timestamp will be used for ordering of events in future release. Resolution: milliseconds (required), microseconds (optimal).
	Timestamp string          `json:"timestamp" url:"timestamp"`
	Metadata  interface{}     `json:"metadata,omitempty" url:"metadata,omitempty"`
	Body      *CreateSpanBody `json:"body,omitempty" url:"body,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (c *CreateSpanEvent) GetId() string {
	if c == nil {
		return ""
	}
	return c.Id
}

func (c *CreateSpanEvent) GetTimestamp() string {
	if c == nil {
		return ""
	}
	return c.Timestamp
}

func (c *CreateSpanEvent) GetMetadata() interface{} {
	if c == nil {
		return nil
	}
	return c.Metadata
}

func (c *CreateSpanEvent) GetBody() *CreateSpanBody {
	if c == nil {
		return nil
	}
	return c.Body
}

func (c *CreateSpanEvent) GetExtraProperties() map[string]interface{} {
	return c.extraProperties
}

func (c *CreateSpanEvent) UnmarshalJSON(data []byte) error {
	type unmarshaler CreateSpanEvent
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*c = CreateSpanEvent(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *c)
	if err != nil {
		return err
	}
	c.extraProperties = extraProperties
	c.rawJSON = json.RawMessage(data)
	return nil
}

func (c *CreateSpanEvent) String() string {
	if len(c.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(c.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(c); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", c)
}

type IngestionError struct {
	Id      string      `json:"id" url:"id"`
	Status  int         `json:"status" url:"status"`
	Message *string     `json:"message,omitempty" url:"message,omitempty"`
	Error   interface{} `json:"error,omitempty" url:"error,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (i *IngestionError) GetId() string {
	if i == nil {
		return ""
	}
	return i.Id
}

func (i *IngestionError) GetStatus() int {
	if i == nil {
		return 0
	}
	return i.Status
}

func (i *IngestionError) GetMessage() *string {
	if i == nil {
		return nil
	}
	return i.Message
}

func (i *IngestionError) GetError() interface{} {
	if i == nil {
		return nil
	}
	return i.Error
}

func (i *IngestionError) GetExtraProperties() map[string]interface{} {
	return i.extraProperties
}

func (i *IngestionError) UnmarshalJSON(data []byte) error {
	type unmarshaler IngestionError
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*i = IngestionError(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *i)
	if err != nil {
		return err
	}
	i.extraProperties = extraProperties
	i.rawJSON = json.RawMessage(data)
	return nil
}

func (i *IngestionError) String() string {
	if len(i.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(i.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(i); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", i)
}

type IngestionEvent struct {
	IngestionEventZero  *IngestionEventZero
	IngestionEventOne   *IngestionEventOne
	IngestionEventTwo   *IngestionEventTwo
	IngestionEventThree *IngestionEventThree
	IngestionEventFour  *IngestionEventFour
	IngestionEventFive  *IngestionEventFive
	IngestionEventSix   *IngestionEventSix
	IngestionEventSeven *IngestionEventSeven
	IngestionEventEight *IngestionEventEight
	IngestionEventNine  *IngestionEventNine

	typ string
}

func NewIngestionEventFromIngestionEventZero(value *IngestionEventZero) *IngestionEvent {
	return &IngestionEvent{typ: "IngestionEventZero", IngestionEventZero: value}
}

func NewIngestionEventFromIngestionEventOne(value *IngestionEventOne) *IngestionEvent {
	return &IngestionEvent{typ: "IngestionEventOne", IngestionEventOne: value}
}

func NewIngestionEventFromIngestionEventTwo(value *IngestionEventTwo) *IngestionEvent {
	return &IngestionEvent{typ: "IngestionEventTwo", IngestionEventTwo: value}
}

func NewIngestionEventFromIngestionEventThree(value *IngestionEventThree) *IngestionEvent {
	return &IngestionEvent{typ: "IngestionEventThree", IngestionEventThree: value}
}

func NewIngestionEventFromIngestionEventFour(value *IngestionEventFour) *IngestionEvent {
	return &IngestionEvent{typ: "IngestionEventFour", IngestionEventFour: value}
}

func NewIngestionEventFromIngestionEventFive(value *IngestionEventFive) *IngestionEvent {
	return &IngestionEvent{typ: "IngestionEventFive", IngestionEventFive: value}
}

func NewIngestionEventFromIngestionEventSix(value *IngestionEventSix) *IngestionEvent {
	return &IngestionEvent{typ: "IngestionEventSix", IngestionEventSix: value}
}

func NewIngestionEventFromIngestionEventSeven(value *IngestionEventSeven) *IngestionEvent {
	return &IngestionEvent{typ: "IngestionEventSeven", IngestionEventSeven: value}
}

func NewIngestionEventFromIngestionEventEight(value *IngestionEventEight) *IngestionEvent {
	return &IngestionEvent{typ: "IngestionEventEight", IngestionEventEight: value}
}

func NewIngestionEventFromIngestionEventNine(value *IngestionEventNine) *IngestionEvent {
	return &IngestionEvent{typ: "IngestionEventNine", IngestionEventNine: value}
}

func (i *IngestionEvent) GetIngestionEventZero() *IngestionEventZero {
	if i == nil {
		return nil
	}
	return i.IngestionEventZero
}

func (i *IngestionEvent) GetIngestionEventOne() *IngestionEventOne {
	if i == nil {
		return nil
	}
	return i.IngestionEventOne
}

func (i *IngestionEvent) GetIngestionEventTwo() *IngestionEventTwo {
	if i == nil {
		return nil
	}
	return i.IngestionEventTwo
}

func (i *IngestionEvent) GetIngestionEventThree() *IngestionEventThree {
	if i == nil {
		return nil
	}
	return i.IngestionEventThree
}

func (i *IngestionEvent) GetIngestionEventFour() *IngestionEventFour {
	if i == nil {
		return nil
	}
	return i.IngestionEventFour
}

func (i *IngestionEvent) GetIngestionEventFive() *IngestionEventFive {
	if i == nil {
		return nil
	}
	return i.IngestionEventFive
}

func (i *IngestionEvent) GetIngestionEventSix() *IngestionEventSix {
	if i == nil {
		return nil
	}
	return i.IngestionEventSix
}

func (i *IngestionEvent) GetIngestionEventSeven() *IngestionEventSeven {
	if i == nil {
		return nil
	}
	return i.IngestionEventSeven
}

func (i *IngestionEvent) GetIngestionEventEight() *IngestionEventEight {
	if i == nil {
		return nil
	}
	return i.IngestionEventEight
}

func (i *IngestionEvent) GetIngestionEventNine() *IngestionEventNine {
	if i == nil {
		return nil
	}
	return i.IngestionEventNine
}

func (i *IngestionEvent) UnmarshalJSON(data []byte) error {
	valueIngestionEventZero := new(IngestionEventZero)
	if err := json.Unmarshal(data, &valueIngestionEventZero); err == nil {
		i.typ = "IngestionEventZero"
		i.IngestionEventZero = valueIngestionEventZero
		return nil
	}
	valueIngestionEventOne := new(IngestionEventOne)
	if err := json.Unmarshal(data, &valueIngestionEventOne); err == nil {
		i.typ = "IngestionEventOne"
		i.IngestionEventOne = valueIngestionEventOne
		return nil
	}
	valueIngestionEventTwo := new(IngestionEventTwo)
	if err := json.Unmarshal(data, &valueIngestionEventTwo); err == nil {
		i.typ = "IngestionEventTwo"
		i.IngestionEventTwo = valueIngestionEventTwo
		return nil
	}
	valueIngestionEventThree := new(IngestionEventThree)
	if err := json.Unmarshal(data, &valueIngestionEventThree); err == nil {
		i.typ = "IngestionEventThree"
		i.IngestionEventThree = valueIngestionEventThree
		return nil
	}
	valueIngestionEventFour := new(IngestionEventFour)
	if err := json.Unmarshal(data, &valueIngestionEventFour); err == nil {
		i.typ = "IngestionEventFour"
		i.IngestionEventFour = valueIngestionEventFour
		return nil
	}
	valueIngestionEventFive := new(IngestionEventFive)
	if err := json.Unmarshal(data, &valueIngestionEventFive); err == nil {
		i.typ = "IngestionEventFive"
		i.IngestionEventFive = valueIngestionEventFive
		return nil
	}
	valueIngestionEventSix := new(IngestionEventSix)
	if err := json.Unmarshal(data, &valueIngestionEventSix); err == nil {
		i.typ = "IngestionEventSix"
		i.IngestionEventSix = valueIngestionEventSix
		return nil
	}
	valueIngestionEventSeven := new(IngestionEventSeven)
	if err := json.Unmarshal(data, &valueIngestionEventSeven); err == nil {
		i.typ = "IngestionEventSeven"
		i.IngestionEventSeven = valueIngestionEventSeven
		return nil
	}
	valueIngestionEventEight := new(IngestionEventEight)
	if err := json.Unmarshal(data, &valueIngestionEventEight); err == nil {
		i.typ = "IngestionEventEight"
		i.IngestionEventEight = valueIngestionEventEight
		return nil
	}
	valueIngestionEventNine := new(IngestionEventNine)
	if err := json.Unmarshal(data, &valueIngestionEventNine); err == nil {
		i.typ = "IngestionEventNine"
		i.IngestionEventNine = valueIngestionEventNine
		return nil
	}
	return fmt.Errorf("%s cannot be deserialized as a %T", data, i)
}

func (i IngestionEvent) MarshalJSON() ([]byte, error) {
	if i.typ == "IngestionEventZero" || i.IngestionEventZero != nil {
		return json.Marshal(i.IngestionEventZero)
	}
	if i.typ == "IngestionEventOne" || i.IngestionEventOne != nil {
		return json.Marshal(i.IngestionEventOne)
	}
	if i.typ == "IngestionEventTwo" || i.IngestionEventTwo != nil {
		return json.Marshal(i.IngestionEventTwo)
	}
	if i.typ == "IngestionEventThree" || i.IngestionEventThree != nil {
		return json.Marshal(i.IngestionEventThree)
	}
	if i.typ == "IngestionEventFour" || i.IngestionEventFour != nil {
		return json.Marshal(i.IngestionEventFour)
	}
	if i.typ == "IngestionEventFive" || i.IngestionEventFive != nil {
		return json.Marshal(i.IngestionEventFive)
	}
	if i.typ == "IngestionEventSix" || i.IngestionEventSix != nil {
		return json.Marshal(i.IngestionEventSix)
	}
	if i.typ == "IngestionEventSeven" || i.IngestionEventSeven != nil {
		return json.Marshal(i.IngestionEventSeven)
	}
	if i.typ == "IngestionEventEight" || i.IngestionEventEight != nil {
		return json.Marshal(i.IngestionEventEight)
	}
	if i.typ == "IngestionEventNine" || i.IngestionEventNine != nil {
		return json.Marshal(i.IngestionEventNine)
	}
	return nil, fmt.Errorf("type %T does not include a non-empty union type", i)
}

type IngestionEventVisitor interface {
	VisitIngestionEventZero(*IngestionEventZero) error
	VisitIngestionEventOne(*IngestionEventOne) error
	VisitIngestionEventTwo(*IngestionEventTwo) error
	VisitIngestionEventThree(*IngestionEventThree) error
	VisitIngestionEventFour(*IngestionEventFour) error
	VisitIngestionEventFive(*IngestionEventFive) error
	VisitIngestionEventSix(*IngestionEventSix) error
	VisitIngestionEventSeven(*IngestionEventSeven) error
	VisitIngestionEventEight(*IngestionEventEight) error
	VisitIngestionEventNine(*IngestionEventNine) error
}

func (i *IngestionEvent) Accept(visitor IngestionEventVisitor) error {
	if i.typ == "IngestionEventZero" || i.IngestionEventZero != nil {
		return visitor.VisitIngestionEventZero(i.IngestionEventZero)
	}
	if i.typ == "IngestionEventOne" || i.IngestionEventOne != nil {
		return visitor.VisitIngestionEventOne(i.IngestionEventOne)
	}
	if i.typ == "IngestionEventTwo" || i.IngestionEventTwo != nil {
		return visitor.VisitIngestionEventTwo(i.IngestionEventTwo)
	}
	if i.typ == "IngestionEventThree" || i.IngestionEventThree != nil {
		return visitor.VisitIngestionEventThree(i.IngestionEventThree)
	}
	if i.typ == "IngestionEventFour" || i.IngestionEventFour != nil {
		return visitor.VisitIngestionEventFour(i.IngestionEventFour)
	}
	if i.typ == "IngestionEventFive" || i.IngestionEventFive != nil {
		return visitor.VisitIngestionEventFive(i.IngestionEventFive)
	}
	if i.typ == "IngestionEventSix" || i.IngestionEventSix != nil {
		return visitor.VisitIngestionEventSix(i.IngestionEventSix)
	}
	if i.typ == "IngestionEventSeven" || i.IngestionEventSeven != nil {
		return visitor.VisitIngestionEventSeven(i.IngestionEventSeven)
	}
	if i.typ == "IngestionEventEight" || i.IngestionEventEight != nil {
		return visitor.VisitIngestionEventEight(i.IngestionEventEight)
	}
	if i.typ == "IngestionEventNine" || i.IngestionEventNine != nil {
		return visitor.VisitIngestionEventNine(i.IngestionEventNine)
	}
	return fmt.Errorf("type %T does not include a non-empty union type", i)
}

type IngestionEventEight struct {
	// UUID v4 that identifies the event
	Id string `json:"id" url:"id"`
	// Datetime (ISO 8601) of event creation in client. Should be as close to actual event creation in client as possible, this timestamp will be used for ordering of events in future release. Resolution: milliseconds (required), microseconds (optimal).
	Timestamp string           `json:"timestamp" url:"timestamp"`
	Metadata  interface{}      `json:"metadata,omitempty" url:"metadata,omitempty"`
	Body      *ObservationBody `json:"body,omitempty" url:"body,omitempty"`
	Type      *string          `json:"type,omitempty" url:"type,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (i *IngestionEventEight) GetId() string {
	if i == nil {
		return ""
	}
	return i.Id
}

func (i *IngestionEventEight) GetTimestamp() string {
	if i == nil {
		return ""
	}
	return i.Timestamp
}

func (i *IngestionEventEight) GetMetadata() interface{} {
	if i == nil {
		return nil
	}
	return i.Metadata
}

func (i *IngestionEventEight) GetBody() *ObservationBody {
	if i == nil {
		return nil
	}
	return i.Body
}

func (i *IngestionEventEight) GetExtraProperties() map[string]interface{} {
	return i.extraProperties
}

func (i *IngestionEventEight) UnmarshalJSON(data []byte) error {
	type unmarshaler IngestionEventEight
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*i = IngestionEventEight(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *i)
	if err != nil {
		return err
	}
	i.extraProperties = extraProperties
	i.rawJSON = json.RawMessage(data)
	return nil
}

func (i *IngestionEventEight) String() string {
	if len(i.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(i.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(i); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", i)
}

type IngestionEventFive struct {
	// UUID v4 that identifies the event
	Id string `json:"id" url:"id"`
	// Datetime (ISO 8601) of event creation in client. Should be as close to actual event creation in client as possible, this timestamp will be used for ordering of events in future release. Resolution: milliseconds (required), microseconds (optimal).
	Timestamp string                `json:"timestamp" url:"timestamp"`
	Metadata  interface{}           `json:"metadata,omitempty" url:"metadata,omitempty"`
	Body      *UpdateGenerationBody `json:"body,omitempty" url:"body,omitempty"`
	Type      *string               `json:"type,omitempty" url:"type,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (i *IngestionEventFive) GetId() string {
	if i == nil {
		return ""
	}
	return i.Id
}

func (i *IngestionEventFive) GetTimestamp() string {
	if i == nil {
		return ""
	}
	return i.Timestamp
}

func (i *IngestionEventFive) GetMetadata() interface{} {
	if i == nil {
		return nil
	}
	return i.Metadata
}

func (i *IngestionEventFive) GetBody() *UpdateGenerationBody {
	if i == nil {
		return nil
	}
	return i.Body
}

func (i *IngestionEventFive) GetExtraProperties() map[string]interface{} {
	return i.extraProperties
}

func (i *IngestionEventFive) UnmarshalJSON(data []byte) error {
	type unmarshaler IngestionEventFive
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*i = IngestionEventFive(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *i)
	if err != nil {
		return err
	}
	i.extraProperties = extraProperties
	i.rawJSON = json.RawMessage(data)
	return nil
}

func (i *IngestionEventFive) String() string {
	if len(i.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(i.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(i); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", i)
}

type IngestionEventFour struct {
	// UUID v4 that identifies the event
	Id string `json:"id" url:"id"`
	// Datetime (ISO 8601) of event creation in client. Should be as close to actual event creation in client as possible, this timestamp will be used for ordering of events in future release. Resolution: milliseconds (required), microseconds (optimal).
	Timestamp string                `json:"timestamp" url:"timestamp"`
	Metadata  interface{}           `json:"metadata,omitempty" url:"metadata,omitempty"`
	Body      *CreateGenerationBody `json:"body,omitempty" url:"body,omitempty"`
	Type      *string               `json:"type,omitempty" url:"type,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (i *IngestionEventFour) GetId() string {
	if i == nil {
		return ""
	}
	return i.Id
}

func (i *IngestionEventFour) GetTimestamp() string {
	if i == nil {
		return ""
	}
	return i.Timestamp
}

func (i *IngestionEventFour) GetMetadata() interface{} {
	if i == nil {
		return nil
	}
	return i.Metadata
}

func (i *IngestionEventFour) GetBody() *CreateGenerationBody {
	if i == nil {
		return nil
	}
	return i.Body
}

func (i *IngestionEventFour) GetExtraProperties() map[string]interface{} {
	return i.extraProperties
}

func (i *IngestionEventFour) UnmarshalJSON(data []byte) error {
	type unmarshaler IngestionEventFour
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*i = IngestionEventFour(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *i)
	if err != nil {
		return err
	}
	i.extraProperties = extraProperties
	i.rawJSON = json.RawMessage(data)
	return nil
}

func (i *IngestionEventFour) String() string {
	if len(i.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(i.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(i); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", i)
}

type IngestionEventNine struct {
	// UUID v4 that identifies the event
	Id string `json:"id" url:"id"`
	// Datetime (ISO 8601) of event creation in client. Should be as close to actual event creation in client as possible, this timestamp will be used for ordering of events in future release. Resolution: milliseconds (required), microseconds (optimal).
	Timestamp string           `json:"timestamp" url:"timestamp"`
	Metadata  interface{}      `json:"metadata,omitempty" url:"metadata,omitempty"`
	Body      *ObservationBody `json:"body,omitempty" url:"body,omitempty"`
	Type      *string          `json:"type,omitempty" url:"type,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (i *IngestionEventNine) GetId() string {
	if i == nil {
		return ""
	}
	return i.Id
}

func (i *IngestionEventNine) GetTimestamp() string {
	if i == nil {
		return ""
	}
	return i.Timestamp
}

func (i *IngestionEventNine) GetMetadata() interface{} {
	if i == nil {
		return nil
	}
	return i.Metadata
}

func (i *IngestionEventNine) GetBody() *ObservationBody {
	if i == nil {
		return nil
	}
	return i.Body
}

func (i *IngestionEventNine) GetExtraProperties() map[string]interface{} {
	return i.extraProperties
}

func (i *IngestionEventNine) UnmarshalJSON(data []byte) error {
	type unmarshaler IngestionEventNine
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*i = IngestionEventNine(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *i)
	if err != nil {
		return err
	}
	i.extraProperties = extraProperties
	i.rawJSON = json.RawMessage(data)
	return nil
}

func (i *IngestionEventNine) String() string {
	if len(i.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(i.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(i); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", i)
}

type IngestionEventOne struct {
	// UUID v4 that identifies the event
	Id string `json:"id" url:"id"`
	// Datetime (ISO 8601) of event creation in client. Should be as close to actual event creation in client as possible, this timestamp will be used for ordering of events in future release. Resolution: milliseconds (required), microseconds (optimal).
	Timestamp string      `json:"timestamp" url:"timestamp"`
	Metadata  interface{} `json:"metadata,omitempty" url:"metadata,omitempty"`
	Body      *ScoreBody  `json:"body,omitempty" url:"body,omitempty"`
	Type      *string     `json:"type,omitempty" url:"type,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (i *IngestionEventOne) GetId() string {
	if i == nil {
		return ""
	}
	return i.Id
}

func (i *IngestionEventOne) GetTimestamp() string {
	if i == nil {
		return ""
	}
	return i.Timestamp
}

func (i *IngestionEventOne) GetMetadata() interface{} {
	if i == nil {
		return nil
	}
	return i.Metadata
}

func (i *IngestionEventOne) GetBody() *ScoreBody {
	if i == nil {
		return nil
	}
	return i.Body
}

func (i *IngestionEventOne) GetExtraProperties() map[string]interface{} {
	return i.extraProperties
}

func (i *IngestionEventOne) UnmarshalJSON(data []byte) error {
	type unmarshaler IngestionEventOne
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*i = IngestionEventOne(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *i)
	if err != nil {
		return err
	}
	i.extraProperties = extraProperties
	i.rawJSON = json.RawMessage(data)
	return nil
}

func (i *IngestionEventOne) String() string {
	if len(i.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(i.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(i); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", i)
}

type IngestionEventSeven struct {
	// UUID v4 that identifies the event
	Id string `json:"id" url:"id"`
	// Datetime (ISO 8601) of event creation in client. Should be as close to actual event creation in client as possible, this timestamp will be used for ordering of events in future release. Resolution: milliseconds (required), microseconds (optimal).
	Timestamp string      `json:"timestamp" url:"timestamp"`
	Metadata  interface{} `json:"metadata,omitempty" url:"metadata,omitempty"`
	Body      *SdkLogBody `json:"body,omitempty" url:"body,omitempty"`
	Type      *string     `json:"type,omitempty" url:"type,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (i *IngestionEventSeven) GetId() string {
	if i == nil {
		return ""
	}
	return i.Id
}

func (i *IngestionEventSeven) GetTimestamp() string {
	if i == nil {
		return ""
	}
	return i.Timestamp
}

func (i *IngestionEventSeven) GetMetadata() interface{} {
	if i == nil {
		return nil
	}
	return i.Metadata
}

func (i *IngestionEventSeven) GetBody() *SdkLogBody {
	if i == nil {
		return nil
	}
	return i.Body
}

func (i *IngestionEventSeven) GetExtraProperties() map[string]interface{} {
	return i.extraProperties
}

func (i *IngestionEventSeven) UnmarshalJSON(data []byte) error {
	type unmarshaler IngestionEventSeven
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*i = IngestionEventSeven(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *i)
	if err != nil {
		return err
	}
	i.extraProperties = extraProperties
	i.rawJSON = json.RawMessage(data)
	return nil
}

func (i *IngestionEventSeven) String() string {
	if len(i.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(i.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(i); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", i)
}

type IngestionEventSix struct {
	// UUID v4 that identifies the event
	Id string `json:"id" url:"id"`
	// Datetime (ISO 8601) of event creation in client. Should be as close to actual event creation in client as possible, this timestamp will be used for ordering of events in future release. Resolution: milliseconds (required), microseconds (optimal).
	Timestamp string           `json:"timestamp" url:"timestamp"`
	Metadata  interface{}      `json:"metadata,omitempty" url:"metadata,omitempty"`
	Body      *CreateEventBody `json:"body,omitempty" url:"body,omitempty"`
	Type      *string          `json:"type,omitempty" url:"type,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (i *IngestionEventSix) GetId() string {
	if i == nil {
		return ""
	}
	return i.Id
}

func (i *IngestionEventSix) GetTimestamp() string {
	if i == nil {
		return ""
	}
	return i.Timestamp
}

func (i *IngestionEventSix) GetMetadata() interface{} {
	if i == nil {
		return nil
	}
	return i.Metadata
}

func (i *IngestionEventSix) GetBody() *CreateEventBody {
	if i == nil {
		return nil
	}
	return i.Body
}

func (i *IngestionEventSix) GetExtraProperties() map[string]interface{} {
	return i.extraProperties
}

func (i *IngestionEventSix) UnmarshalJSON(data []byte) error {
	type unmarshaler IngestionEventSix
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*i = IngestionEventSix(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *i)
	if err != nil {
		return err
	}
	i.extraProperties = extraProperties
	i.rawJSON = json.RawMessage(data)
	return nil
}

func (i *IngestionEventSix) String() string {
	if len(i.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(i.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(i); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", i)
}

type IngestionEventThree struct {
	// UUID v4 that identifies the event
	Id string `json:"id" url:"id"`
	// Datetime (ISO 8601) of event creation in client. Should be as close to actual event creation in client as possible, this timestamp will be used for ordering of events in future release. Resolution: milliseconds (required), microseconds (optimal).
	Timestamp string          `json:"timestamp" url:"timestamp"`
	Metadata  interface{}     `json:"metadata,omitempty" url:"metadata,omitempty"`
	Body      *UpdateSpanBody `json:"body,omitempty" url:"body,omitempty"`
	Type      *string         `json:"type,omitempty" url:"type,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (i *IngestionEventThree) GetId() string {
	if i == nil {
		return ""
	}
	return i.Id
}

func (i *IngestionEventThree) GetTimestamp() string {
	if i == nil {
		return ""
	}
	return i.Timestamp
}

func (i *IngestionEventThree) GetMetadata() interface{} {
	if i == nil {
		return nil
	}
	return i.Metadata
}

func (i *IngestionEventThree) GetBody() *UpdateSpanBody {
	if i == nil {
		return nil
	}
	return i.Body
}

func (i *IngestionEventThree) GetExtraProperties() map[string]interface{} {
	return i.extraProperties
}

func (i *IngestionEventThree) UnmarshalJSON(data []byte) error {
	type unmarshaler IngestionEventThree
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*i = IngestionEventThree(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *i)
	if err != nil {
		return err
	}
	i.extraProperties = extraProperties
	i.rawJSON = json.RawMessage(data)
	return nil
}

func (i *IngestionEventThree) String() string {
	if len(i.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(i.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(i); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", i)
}

type IngestionEventTwo struct {
	// UUID v4 that identifies the event
	Id string `json:"id" url:"id"`
	// Datetime (ISO 8601) of event creation in client. Should be as close to actual event creation in client as possible, this timestamp will be used for ordering of events in future release. Resolution: milliseconds (required), microseconds (optimal).
	Timestamp string          `json:"timestamp" url:"timestamp"`
	Metadata  interface{}     `json:"metadata,omitempty" url:"metadata,omitempty"`
	Body      *CreateSpanBody `json:"body,omitempty" url:"body,omitempty"`
	Type      *string         `json:"type,omitempty" url:"type,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (i *IngestionEventTwo) GetId() string {
	if i == nil {
		return ""
	}
	return i.Id
}

func (i *IngestionEventTwo) GetTimestamp() string {
	if i == nil {
		return ""
	}
	return i.Timestamp
}

func (i *IngestionEventTwo) GetMetadata() interface{} {
	if i == nil {
		return nil
	}
	return i.Metadata
}

func (i *IngestionEventTwo) GetBody() *CreateSpanBody {
	if i == nil {
		return nil
	}
	return i.Body
}

func (i *IngestionEventTwo) GetExtraProperties() map[string]interface{} {
	return i.extraProperties
}

func (i *IngestionEventTwo) UnmarshalJSON(data []byte) error {
	type unmarshaler IngestionEventTwo
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*i = IngestionEventTwo(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *i)
	if err != nil {
		return err
	}
	i.extraProperties = extraProperties
	i.rawJSON = json.RawMessage(data)
	return nil
}

func (i *IngestionEventTwo) String() string {
	if len(i.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(i.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(i); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", i)
}

type IngestionEventZero struct {
	// UUID v4 that identifies the event
	Id string `json:"id" url:"id"`
	// Datetime (ISO 8601) of event creation in client. Should be as close to actual event creation in client as possible, this timestamp will be used for ordering of events in future release. Resolution: milliseconds (required), microseconds (optimal).
	Timestamp string      `json:"timestamp" url:"timestamp"`
	Metadata  interface{} `json:"metadata,omitempty" url:"metadata,omitempty"`
	Body      *TraceBody  `json:"body,omitempty" url:"body,omitempty"`
	Type      *string     `json:"type,omitempty" url:"type,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (i *IngestionEventZero) GetId() string {
	if i == nil {
		return ""
	}
	return i.Id
}

func (i *IngestionEventZero) GetTimestamp() string {
	if i == nil {
		return ""
	}
	return i.Timestamp
}

func (i *IngestionEventZero) GetMetadata() interface{} {
	if i == nil {
		return nil
	}
	return i.Metadata
}

func (i *IngestionEventZero) GetBody() *TraceBody {
	if i == nil {
		return nil
	}
	return i.Body
}

func (i *IngestionEventZero) GetExtraProperties() map[string]interface{} {
	return i.extraProperties
}

func (i *IngestionEventZero) UnmarshalJSON(data []byte) error {
	type unmarshaler IngestionEventZero
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*i = IngestionEventZero(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *i)
	if err != nil {
		return err
	}
	i.extraProperties = extraProperties
	i.rawJSON = json.RawMessage(data)
	return nil
}

func (i *IngestionEventZero) String() string {
	if len(i.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(i.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(i); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", i)
}

type IngestionResponse struct {
	Successes []*IngestionSuccess `json:"successes,omitempty" url:"successes,omitempty"`
	Errors    []*IngestionError   `json:"errors,omitempty" url:"errors,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (i *IngestionResponse) GetSuccesses() []*IngestionSuccess {
	if i == nil {
		return nil
	}
	return i.Successes
}

func (i *IngestionResponse) GetErrors() []*IngestionError {
	if i == nil {
		return nil
	}
	return i.Errors
}

func (i *IngestionResponse) GetExtraProperties() map[string]interface{} {
	return i.extraProperties
}

func (i *IngestionResponse) UnmarshalJSON(data []byte) error {
	type unmarshaler IngestionResponse
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*i = IngestionResponse(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *i)
	if err != nil {
		return err
	}
	i.extraProperties = extraProperties
	i.rawJSON = json.RawMessage(data)
	return nil
}

func (i *IngestionResponse) String() string {
	if len(i.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(i.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(i); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", i)
}

type IngestionSuccess struct {
	Id     string `json:"id" url:"id"`
	Status int    `json:"status" url:"status"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (i *IngestionSuccess) GetId() string {
	if i == nil {
		return ""
	}
	return i.Id
}

func (i *IngestionSuccess) GetStatus() int {
	if i == nil {
		return 0
	}
	return i.Status
}

func (i *IngestionSuccess) GetExtraProperties() map[string]interface{} {
	return i.extraProperties
}

func (i *IngestionSuccess) UnmarshalJSON(data []byte) error {
	type unmarshaler IngestionSuccess
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*i = IngestionSuccess(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *i)
	if err != nil {
		return err
	}
	i.extraProperties = extraProperties
	i.rawJSON = json.RawMessage(data)
	return nil
}

func (i *IngestionSuccess) String() string {
	if len(i.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(i.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(i); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", i)
}

type IngestionUsage struct {
	Usage       *Usage
	OpenAiUsage *OpenAiUsage

	typ string
}

func NewIngestionUsageFromUsage(value *Usage) *IngestionUsage {
	return &IngestionUsage{typ: "Usage", Usage: value}
}

func NewIngestionUsageFromOpenAiUsage(value *OpenAiUsage) *IngestionUsage {
	return &IngestionUsage{typ: "OpenAiUsage", OpenAiUsage: value}
}

func (i *IngestionUsage) GetUsage() *Usage {
	if i == nil {
		return nil
	}
	return i.Usage
}

func (i *IngestionUsage) GetOpenAiUsage() *OpenAiUsage {
	if i == nil {
		return nil
	}
	return i.OpenAiUsage
}

func (i *IngestionUsage) UnmarshalJSON(data []byte) error {
	valueUsage := new(Usage)
	if err := json.Unmarshal(data, &valueUsage); err == nil {
		i.typ = "Usage"
		i.Usage = valueUsage
		return nil
	}
	valueOpenAiUsage := new(OpenAiUsage)
	if err := json.Unmarshal(data, &valueOpenAiUsage); err == nil {
		i.typ = "OpenAiUsage"
		i.OpenAiUsage = valueOpenAiUsage
		return nil
	}
	return fmt.Errorf("%s cannot be deserialized as a %T", data, i)
}

func (i IngestionUsage) MarshalJSON() ([]byte, error) {
	if i.typ == "Usage" || i.Usage != nil {
		return json.Marshal(i.Usage)
	}
	if i.typ == "OpenAiUsage" || i.OpenAiUsage != nil {
		return json.Marshal(i.OpenAiUsage)
	}
	return nil, fmt.Errorf("type %T does not include a non-empty union type", i)
}

type IngestionUsageVisitor interface {
	VisitUsage(*Usage) error
	VisitOpenAiUsage(*OpenAiUsage) error
}

func (i *IngestionUsage) Accept(visitor IngestionUsageVisitor) error {
	if i.typ == "Usage" || i.Usage != nil {
		return visitor.VisitUsage(i.Usage)
	}
	if i.typ == "OpenAiUsage" || i.OpenAiUsage != nil {
		return visitor.VisitOpenAiUsage(i.OpenAiUsage)
	}
	return fmt.Errorf("type %T does not include a non-empty union type", i)
}

type ObservationBody struct {
	Id                  *string              `json:"id,omitempty" url:"id,omitempty"`
	TraceId             *string              `json:"traceId,omitempty" url:"traceId,omitempty"`
	Type                ObservationType      `json:"type" url:"type"`
	Name                *string              `json:"name,omitempty" url:"name,omitempty"`
	StartTime           *time.Time           `json:"startTime,omitempty" url:"startTime,omitempty"`
	EndTime             *time.Time           `json:"endTime,omitempty" url:"endTime,omitempty"`
	CompletionStartTime *time.Time           `json:"completionStartTime,omitempty" url:"completionStartTime,omitempty"`
	Model               *string              `json:"model,omitempty" url:"model,omitempty"`
	ModelParameters     map[string]*MapValue `json:"modelParameters,omitempty" url:"modelParameters,omitempty"`
	Input               interface{}          `json:"input,omitempty" url:"input,omitempty"`
	Version             *string              `json:"version,omitempty" url:"version,omitempty"`
	Metadata            interface{}          `json:"metadata,omitempty" url:"metadata,omitempty"`
	Output              interface{}          `json:"output,omitempty" url:"output,omitempty"`
	Usage               *Usage               `json:"usage,omitempty" url:"usage,omitempty"`
	Level               *ObservationLevel    `json:"level,omitempty" url:"level,omitempty"`
	StatusMessage       *string              `json:"statusMessage,omitempty" url:"statusMessage,omitempty"`
	ParentObservationId *string              `json:"parentObservationId,omitempty" url:"parentObservationId,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (o *ObservationBody) GetId() *string {
	if o == nil {
		return nil
	}
	return o.Id
}

func (o *ObservationBody) GetTraceId() *string {
	if o == nil {
		return nil
	}
	return o.TraceId
}

func (o *ObservationBody) GetType() ObservationType {
	if o == nil {
		return ""
	}
	return o.Type
}

func (o *ObservationBody) GetName() *string {
	if o == nil {
		return nil
	}
	return o.Name
}

func (o *ObservationBody) GetStartTime() *time.Time {
	if o == nil {
		return nil
	}
	return o.StartTime
}

func (o *ObservationBody) GetEndTime() *time.Time {
	if o == nil {
		return nil
	}
	return o.EndTime
}

func (o *ObservationBody) GetCompletionStartTime() *time.Time {
	if o == nil {
		return nil
	}
	return o.CompletionStartTime
}

func (o *ObservationBody) GetModel() *string {
	if o == nil {
		return nil
	}
	return o.Model
}

func (o *ObservationBody) GetModelParameters() map[string]*MapValue {
	if o == nil {
		return nil
	}
	return o.ModelParameters
}

func (o *ObservationBody) GetInput() interface{} {
	if o == nil {
		return nil
	}
	return o.Input
}

func (o *ObservationBody) GetVersion() *string {
	if o == nil {
		return nil
	}
	return o.Version
}

func (o *ObservationBody) GetMetadata() interface{} {
	if o == nil {
		return nil
	}
	return o.Metadata
}

func (o *ObservationBody) GetOutput() interface{} {
	if o == nil {
		return nil
	}
	return o.Output
}

func (o *ObservationBody) GetUsage() *Usage {
	if o == nil {
		return nil
	}
	return o.Usage
}

func (o *ObservationBody) GetLevel() *ObservationLevel {
	if o == nil {
		return nil
	}
	return o.Level
}

func (o *ObservationBody) GetStatusMessage() *string {
	if o == nil {
		return nil
	}
	return o.StatusMessage
}

func (o *ObservationBody) GetParentObservationId() *string {
	if o == nil {
		return nil
	}
	return o.ParentObservationId
}

func (o *ObservationBody) GetExtraProperties() map[string]interface{} {
	return o.extraProperties
}

func (o *ObservationBody) UnmarshalJSON(data []byte) error {
	type embed ObservationBody
	var unmarshaler = struct {
		embed
		StartTime           *internal.DateTime `json:"startTime,omitempty"`
		EndTime             *internal.DateTime `json:"endTime,omitempty"`
		CompletionStartTime *internal.DateTime `json:"completionStartTime,omitempty"`
	}{
		embed: embed(*o),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*o = ObservationBody(unmarshaler.embed)
	o.StartTime = unmarshaler.StartTime.TimePtr()
	o.EndTime = unmarshaler.EndTime.TimePtr()
	o.CompletionStartTime = unmarshaler.CompletionStartTime.TimePtr()
	extraProperties, err := internal.ExtractExtraProperties(data, *o)
	if err != nil {
		return err
	}
	o.extraProperties = extraProperties
	o.rawJSON = json.RawMessage(data)
	return nil
}

func (o *ObservationBody) MarshalJSON() ([]byte, error) {
	type embed ObservationBody
	var marshaler = struct {
		embed
		StartTime           *internal.DateTime `json:"startTime,omitempty"`
		EndTime             *internal.DateTime `json:"endTime,omitempty"`
		CompletionStartTime *internal.DateTime `json:"completionStartTime,omitempty"`
	}{
		embed:               embed(*o),
		StartTime:           internal.NewOptionalDateTime(o.StartTime),
		EndTime:             internal.NewOptionalDateTime(o.EndTime),
		CompletionStartTime: internal.NewOptionalDateTime(o.CompletionStartTime),
	}
	return json.Marshal(marshaler)
}

func (o *ObservationBody) String() string {
	if len(o.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(o.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(o); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", o)
}

type ObservationType string

const (
	ObservationTypeSpan       ObservationType = "SPAN"
	ObservationTypeGeneration ObservationType = "GENERATION"
	ObservationTypeEvent      ObservationType = "EVENT"
)

func NewObservationTypeFromString(s string) (ObservationType, error) {
	switch s {
	case "SPAN":
		return ObservationTypeSpan, nil
	case "GENERATION":
		return ObservationTypeGeneration, nil
	case "EVENT":
		return ObservationTypeEvent, nil
	}
	var t ObservationType
	return "", fmt.Errorf("%s is not a valid %T", s, t)
}

func (o ObservationType) Ptr() *ObservationType {
	return &o
}

// Usage interface of OpenAI for improved compatibility.
type OpenAiUsage struct {
	PromptTokens     *int `json:"promptTokens,omitempty" url:"promptTokens,omitempty"`
	CompletionTokens *int `json:"completionTokens,omitempty" url:"completionTokens,omitempty"`
	TotalTokens      *int `json:"totalTokens,omitempty" url:"totalTokens,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (o *OpenAiUsage) GetPromptTokens() *int {
	if o == nil {
		return nil
	}
	return o.PromptTokens
}

func (o *OpenAiUsage) GetCompletionTokens() *int {
	if o == nil {
		return nil
	}
	return o.CompletionTokens
}

func (o *OpenAiUsage) GetTotalTokens() *int {
	if o == nil {
		return nil
	}
	return o.TotalTokens
}

func (o *OpenAiUsage) GetExtraProperties() map[string]interface{} {
	return o.extraProperties
}

func (o *OpenAiUsage) UnmarshalJSON(data []byte) error {
	type unmarshaler OpenAiUsage
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*o = OpenAiUsage(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *o)
	if err != nil {
		return err
	}
	o.extraProperties = extraProperties
	o.rawJSON = json.RawMessage(data)
	return nil
}

func (o *OpenAiUsage) String() string {
	if len(o.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(o.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(o); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", o)
}

type OptionalObservationBody struct {
	TraceId             *string           `json:"traceId,omitempty" url:"traceId,omitempty"`
	Name                *string           `json:"name,omitempty" url:"name,omitempty"`
	StartTime           *time.Time        `json:"startTime,omitempty" url:"startTime,omitempty"`
	Metadata            interface{}       `json:"metadata,omitempty" url:"metadata,omitempty"`
	Input               interface{}       `json:"input,omitempty" url:"input,omitempty"`
	Output              interface{}       `json:"output,omitempty" url:"output,omitempty"`
	Level               *ObservationLevel `json:"level,omitempty" url:"level,omitempty"`
	StatusMessage       *string           `json:"statusMessage,omitempty" url:"statusMessage,omitempty"`
	ParentObservationId *string           `json:"parentObservationId,omitempty" url:"parentObservationId,omitempty"`
	Version             *string           `json:"version,omitempty" url:"version,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (o *OptionalObservationBody) GetTraceId() *string {
	if o == nil {
		return nil
	}
	return o.TraceId
}

func (o *OptionalObservationBody) GetName() *string {
	if o == nil {
		return nil
	}
	return o.Name
}

func (o *OptionalObservationBody) GetStartTime() *time.Time {
	if o == nil {
		return nil
	}
	return o.StartTime
}

func (o *OptionalObservationBody) GetMetadata() interface{} {
	if o == nil {
		return nil
	}
	return o.Metadata
}

func (o *OptionalObservationBody) GetInput() interface{} {
	if o == nil {
		return nil
	}
	return o.Input
}

func (o *OptionalObservationBody) GetOutput() interface{} {
	if o == nil {
		return nil
	}
	return o.Output
}

func (o *OptionalObservationBody) GetLevel() *ObservationLevel {
	if o == nil {
		return nil
	}
	return o.Level
}

func (o *OptionalObservationBody) GetStatusMessage() *string {
	if o == nil {
		return nil
	}
	return o.StatusMessage
}

func (o *OptionalObservationBody) GetParentObservationId() *string {
	if o == nil {
		return nil
	}
	return o.ParentObservationId
}

func (o *OptionalObservationBody) GetVersion() *string {
	if o == nil {
		return nil
	}
	return o.Version
}

func (o *OptionalObservationBody) GetExtraProperties() map[string]interface{} {
	return o.extraProperties
}

func (o *OptionalObservationBody) UnmarshalJSON(data []byte) error {
	type embed OptionalObservationBody
	var unmarshaler = struct {
		embed
		StartTime *internal.DateTime `json:"startTime,omitempty"`
	}{
		embed: embed(*o),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*o = OptionalObservationBody(unmarshaler.embed)
	o.StartTime = unmarshaler.StartTime.TimePtr()
	extraProperties, err := internal.ExtractExtraProperties(data, *o)
	if err != nil {
		return err
	}
	o.extraProperties = extraProperties
	o.rawJSON = json.RawMessage(data)
	return nil
}

func (o *OptionalObservationBody) MarshalJSON() ([]byte, error) {
	type embed OptionalObservationBody
	var marshaler = struct {
		embed
		StartTime *internal.DateTime `json:"startTime,omitempty"`
	}{
		embed:     embed(*o),
		StartTime: internal.NewOptionalDateTime(o.StartTime),
	}
	return json.Marshal(marshaler)
}

func (o *OptionalObservationBody) String() string {
	if len(o.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(o.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(o); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", o)
}

type SdkLogBody struct {
	Log interface{} `json:"log,omitempty" url:"log,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (s *SdkLogBody) GetLog() interface{} {
	if s == nil {
		return nil
	}
	return s.Log
}

func (s *SdkLogBody) GetExtraProperties() map[string]interface{} {
	return s.extraProperties
}

func (s *SdkLogBody) UnmarshalJSON(data []byte) error {
	type unmarshaler SdkLogBody
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*s = SdkLogBody(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *s)
	if err != nil {
		return err
	}
	s.extraProperties = extraProperties
	s.rawJSON = json.RawMessage(data)
	return nil
}

func (s *SdkLogBody) String() string {
	if len(s.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(s.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(s); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", s)
}

type SdkLogEvent struct {
	// UUID v4 that identifies the event
	Id string `json:"id" url:"id"`
	// Datetime (ISO 8601) of event creation in client. Should be as close to actual event creation in client as possible, this timestamp will be used for ordering of events in future release. Resolution: milliseconds (required), microseconds (optimal).
	Timestamp string      `json:"timestamp" url:"timestamp"`
	Metadata  interface{} `json:"metadata,omitempty" url:"metadata,omitempty"`
	Body      *SdkLogBody `json:"body,omitempty" url:"body,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (s *SdkLogEvent) GetId() string {
	if s == nil {
		return ""
	}
	return s.Id
}

func (s *SdkLogEvent) GetTimestamp() string {
	if s == nil {
		return ""
	}
	return s.Timestamp
}

func (s *SdkLogEvent) GetMetadata() interface{} {
	if s == nil {
		return nil
	}
	return s.Metadata
}

func (s *SdkLogEvent) GetBody() *SdkLogBody {
	if s == nil {
		return nil
	}
	return s.Body
}

func (s *SdkLogEvent) GetExtraProperties() map[string]interface{} {
	return s.extraProperties
}

func (s *SdkLogEvent) UnmarshalJSON(data []byte) error {
	type unmarshaler SdkLogEvent
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*s = SdkLogEvent(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *s)
	if err != nil {
		return err
	}
	s.extraProperties = extraProperties
	s.rawJSON = json.RawMessage(data)
	return nil
}

func (s *SdkLogEvent) String() string {
	if len(s.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(s.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(s); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", s)
}

type ScoreBody struct {
	Id      *string `json:"id,omitempty" url:"id,omitempty"`
	TraceId string  `json:"traceId" url:"traceId"`
	Name    string  `json:"name" url:"name"`
	// The value of the score. Must be passed as string for categorical scores, and numeric for boolean and numeric scores. Boolean score values must equal either 1 or 0 (true or false)
	Value         *CreateScoreValue `json:"value,omitempty" url:"value,omitempty"`
	ObservationId *string           `json:"observationId,omitempty" url:"observationId,omitempty"`
	Comment       *string           `json:"comment,omitempty" url:"comment,omitempty"`
	// When set, must match the score value's type. If not set, will be inferred from the score value or config
	DataType *ScoreDataType `json:"dataType,omitempty" url:"dataType,omitempty"`
	// Reference a score config on a score. When set, the score name must equal the config name and scores must comply with the config's range and data type. For categorical scores, the value must map to a config category. Numeric scores might be constrained by the score config's max and min values
	ConfigId *string `json:"configId,omitempty" url:"configId,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (s *ScoreBody) GetId() *string {
	if s == nil {
		return nil
	}
	return s.Id
}

func (s *ScoreBody) GetTraceId() string {
	if s == nil {
		return ""
	}
	return s.TraceId
}

func (s *ScoreBody) GetName() string {
	if s == nil {
		return ""
	}
	return s.Name
}

func (s *ScoreBody) GetValue() *CreateScoreValue {
	if s == nil {
		return nil
	}
	return s.Value
}

func (s *ScoreBody) GetObservationId() *string {
	if s == nil {
		return nil
	}
	return s.ObservationId
}

func (s *ScoreBody) GetComment() *string {
	if s == nil {
		return nil
	}
	return s.Comment
}

func (s *ScoreBody) GetDataType() *ScoreDataType {
	if s == nil {
		return nil
	}
	return s.DataType
}

func (s *ScoreBody) GetConfigId() *string {
	if s == nil {
		return nil
	}
	return s.ConfigId
}

func (s *ScoreBody) GetExtraProperties() map[string]interface{} {
	return s.extraProperties
}

func (s *ScoreBody) UnmarshalJSON(data []byte) error {
	type unmarshaler ScoreBody
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*s = ScoreBody(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *s)
	if err != nil {
		return err
	}
	s.extraProperties = extraProperties
	s.rawJSON = json.RawMessage(data)
	return nil
}

func (s *ScoreBody) String() string {
	if len(s.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(s.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(s); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", s)
}

type ScoreEvent struct {
	// UUID v4 that identifies the event
	Id string `json:"id" url:"id"`
	// Datetime (ISO 8601) of event creation in client. Should be as close to actual event creation in client as possible, this timestamp will be used for ordering of events in future release. Resolution: milliseconds (required), microseconds (optimal).
	Timestamp string      `json:"timestamp" url:"timestamp"`
	Metadata  interface{} `json:"metadata,omitempty" url:"metadata,omitempty"`
	Body      *ScoreBody  `json:"body,omitempty" url:"body,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (s *ScoreEvent) GetId() string {
	if s == nil {
		return ""
	}
	return s.Id
}

func (s *ScoreEvent) GetTimestamp() string {
	if s == nil {
		return ""
	}
	return s.Timestamp
}

func (s *ScoreEvent) GetMetadata() interface{} {
	if s == nil {
		return nil
	}
	return s.Metadata
}

func (s *ScoreEvent) GetBody() *ScoreBody {
	if s == nil {
		return nil
	}
	return s.Body
}

func (s *ScoreEvent) GetExtraProperties() map[string]interface{} {
	return s.extraProperties
}

func (s *ScoreEvent) UnmarshalJSON(data []byte) error {
	type unmarshaler ScoreEvent
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*s = ScoreEvent(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *s)
	if err != nil {
		return err
	}
	s.extraProperties = extraProperties
	s.rawJSON = json.RawMessage(data)
	return nil
}

func (s *ScoreEvent) String() string {
	if len(s.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(s.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(s); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", s)
}

type TraceBody struct {
	Id        *string     `json:"id,omitempty" url:"id,omitempty"`
	Timestamp *time.Time  `json:"timestamp,omitempty" url:"timestamp,omitempty"`
	Name      *string     `json:"name,omitempty" url:"name,omitempty"`
	UserId    *string     `json:"userId,omitempty" url:"userId,omitempty"`
	Input     interface{} `json:"input,omitempty" url:"input,omitempty"`
	Output    interface{} `json:"output,omitempty" url:"output,omitempty"`
	SessionId *string     `json:"sessionId,omitempty" url:"sessionId,omitempty"`
	Release   *string     `json:"release,omitempty" url:"release,omitempty"`
	Version   *string     `json:"version,omitempty" url:"version,omitempty"`
	Metadata  interface{} `json:"metadata,omitempty" url:"metadata,omitempty"`
	Tags      []string    `json:"tags,omitempty" url:"tags,omitempty"`
	// Make trace publicly accessible via url
	Public *bool `json:"public,omitempty" url:"public,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (t *TraceBody) GetId() *string {
	if t == nil {
		return nil
	}
	return t.Id
}

func (t *TraceBody) GetTimestamp() *time.Time {
	if t == nil {
		return nil
	}
	return t.Timestamp
}

func (t *TraceBody) GetName() *string {
	if t == nil {
		return nil
	}
	return t.Name
}

func (t *TraceBody) GetUserId() *string {
	if t == nil {
		return nil
	}
	return t.UserId
}

func (t *TraceBody) GetInput() interface{} {
	if t == nil {
		return nil
	}
	return t.Input
}

func (t *TraceBody) GetOutput() interface{} {
	if t == nil {
		return nil
	}
	return t.Output
}

func (t *TraceBody) GetSessionId() *string {
	if t == nil {
		return nil
	}
	return t.SessionId
}

func (t *TraceBody) GetRelease() *string {
	if t == nil {
		return nil
	}
	return t.Release
}

func (t *TraceBody) GetVersion() *string {
	if t == nil {
		return nil
	}
	return t.Version
}

func (t *TraceBody) GetMetadata() interface{} {
	if t == nil {
		return nil
	}
	return t.Metadata
}

func (t *TraceBody) GetTags() []string {
	if t == nil {
		return nil
	}
	return t.Tags
}

func (t *TraceBody) GetPublic() *bool {
	if t == nil {
		return nil
	}
	return t.Public
}

func (t *TraceBody) GetExtraProperties() map[string]interface{} {
	return t.extraProperties
}

func (t *TraceBody) UnmarshalJSON(data []byte) error {
	type embed TraceBody
	var unmarshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp,omitempty"`
	}{
		embed: embed(*t),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*t = TraceBody(unmarshaler.embed)
	t.Timestamp = unmarshaler.Timestamp.TimePtr()
	extraProperties, err := internal.ExtractExtraProperties(data, *t)
	if err != nil {
		return err
	}
	t.extraProperties = extraProperties
	t.rawJSON = json.RawMessage(data)
	return nil
}

func (t *TraceBody) MarshalJSON() ([]byte, error) {
	type embed TraceBody
	var marshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp,omitempty"`
	}{
		embed:     embed(*t),
		Timestamp: internal.NewOptionalDateTime(t.Timestamp),
	}
	return json.Marshal(marshaler)
}

func (t *TraceBody) String() string {
	if len(t.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(t.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(t); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", t)
}

type TraceEvent struct {
	// UUID v4 that identifies the event
	Id string `json:"id" url:"id"`
	// Datetime (ISO 8601) of event creation in client. Should be as close to actual event creation in client as possible, this timestamp will be used for ordering of events in future release. Resolution: milliseconds (required), microseconds (optimal).
	Timestamp string      `json:"timestamp" url:"timestamp"`
	Metadata  interface{} `json:"metadata,omitempty" url:"metadata,omitempty"`
	Body      *TraceBody  `json:"body,omitempty" url:"body,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (t *TraceEvent) GetId() string {
	if t == nil {
		return ""
	}
	return t.Id
}

func (t *TraceEvent) GetTimestamp() string {
	if t == nil {
		return ""
	}
	return t.Timestamp
}

func (t *TraceEvent) GetMetadata() interface{} {
	if t == nil {
		return nil
	}
	return t.Metadata
}

func (t *TraceEvent) GetBody() *TraceBody {
	if t == nil {
		return nil
	}
	return t.Body
}

func (t *TraceEvent) GetExtraProperties() map[string]interface{} {
	return t.extraProperties
}

func (t *TraceEvent) UnmarshalJSON(data []byte) error {
	type unmarshaler TraceEvent
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*t = TraceEvent(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *t)
	if err != nil {
		return err
	}
	t.extraProperties = extraProperties
	t.rawJSON = json.RawMessage(data)
	return nil
}

func (t *TraceEvent) String() string {
	if len(t.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(t.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(t); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", t)
}

type UpdateEventBody struct {
	TraceId             *string           `json:"traceId,omitempty" url:"traceId,omitempty"`
	Name                *string           `json:"name,omitempty" url:"name,omitempty"`
	StartTime           *time.Time        `json:"startTime,omitempty" url:"startTime,omitempty"`
	Metadata            interface{}       `json:"metadata,omitempty" url:"metadata,omitempty"`
	Input               interface{}       `json:"input,omitempty" url:"input,omitempty"`
	Output              interface{}       `json:"output,omitempty" url:"output,omitempty"`
	Level               *ObservationLevel `json:"level,omitempty" url:"level,omitempty"`
	StatusMessage       *string           `json:"statusMessage,omitempty" url:"statusMessage,omitempty"`
	ParentObservationId *string           `json:"parentObservationId,omitempty" url:"parentObservationId,omitempty"`
	Version             *string           `json:"version,omitempty" url:"version,omitempty"`
	Id                  string            `json:"id" url:"id"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (u *UpdateEventBody) GetTraceId() *string {
	if u == nil {
		return nil
	}
	return u.TraceId
}

func (u *UpdateEventBody) GetName() *string {
	if u == nil {
		return nil
	}
	return u.Name
}

func (u *UpdateEventBody) GetStartTime() *time.Time {
	if u == nil {
		return nil
	}
	return u.StartTime
}

func (u *UpdateEventBody) GetMetadata() interface{} {
	if u == nil {
		return nil
	}
	return u.Metadata
}

func (u *UpdateEventBody) GetInput() interface{} {
	if u == nil {
		return nil
	}
	return u.Input
}

func (u *UpdateEventBody) GetOutput() interface{} {
	if u == nil {
		return nil
	}
	return u.Output
}

func (u *UpdateEventBody) GetLevel() *ObservationLevel {
	if u == nil {
		return nil
	}
	return u.Level
}

func (u *UpdateEventBody) GetStatusMessage() *string {
	if u == nil {
		return nil
	}
	return u.StatusMessage
}

func (u *UpdateEventBody) GetParentObservationId() *string {
	if u == nil {
		return nil
	}
	return u.ParentObservationId
}

func (u *UpdateEventBody) GetVersion() *string {
	if u == nil {
		return nil
	}
	return u.Version
}

func (u *UpdateEventBody) GetId() string {
	if u == nil {
		return ""
	}
	return u.Id
}

func (u *UpdateEventBody) GetExtraProperties() map[string]interface{} {
	return u.extraProperties
}

func (u *UpdateEventBody) UnmarshalJSON(data []byte) error {
	type embed UpdateEventBody
	var unmarshaler = struct {
		embed
		StartTime *internal.DateTime `json:"startTime,omitempty"`
	}{
		embed: embed(*u),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*u = UpdateEventBody(unmarshaler.embed)
	u.StartTime = unmarshaler.StartTime.TimePtr()
	extraProperties, err := internal.ExtractExtraProperties(data, *u)
	if err != nil {
		return err
	}
	u.extraProperties = extraProperties
	u.rawJSON = json.RawMessage(data)
	return nil
}

func (u *UpdateEventBody) MarshalJSON() ([]byte, error) {
	type embed UpdateEventBody
	var marshaler = struct {
		embed
		StartTime *internal.DateTime `json:"startTime,omitempty"`
	}{
		embed:     embed(*u),
		StartTime: internal.NewOptionalDateTime(u.StartTime),
	}
	return json.Marshal(marshaler)
}

func (u *UpdateEventBody) String() string {
	if len(u.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(u.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(u); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", u)
}

type UpdateGenerationBody struct {
	TraceId             *string              `json:"traceId,omitempty" url:"traceId,omitempty"`
	Name                *string              `json:"name,omitempty" url:"name,omitempty"`
	StartTime           *time.Time           `json:"startTime,omitempty" url:"startTime,omitempty"`
	Metadata            interface{}          `json:"metadata,omitempty" url:"metadata,omitempty"`
	Input               interface{}          `json:"input,omitempty" url:"input,omitempty"`
	Output              interface{}          `json:"output,omitempty" url:"output,omitempty"`
	Level               *ObservationLevel    `json:"level,omitempty" url:"level,omitempty"`
	StatusMessage       *string              `json:"statusMessage,omitempty" url:"statusMessage,omitempty"`
	ParentObservationId *string              `json:"parentObservationId,omitempty" url:"parentObservationId,omitempty"`
	Version             *string              `json:"version,omitempty" url:"version,omitempty"`
	Id                  string               `json:"id" url:"id"`
	EndTime             *time.Time           `json:"endTime,omitempty" url:"endTime,omitempty"`
	CompletionStartTime *time.Time           `json:"completionStartTime,omitempty" url:"completionStartTime,omitempty"`
	Model               *string              `json:"model,omitempty" url:"model,omitempty"`
	ModelParameters     map[string]*MapValue `json:"modelParameters,omitempty" url:"modelParameters,omitempty"`
	Usage               *IngestionUsage      `json:"usage,omitempty" url:"usage,omitempty"`
	PromptName          *string              `json:"promptName,omitempty" url:"promptName,omitempty"`
	PromptVersion       *int                 `json:"promptVersion,omitempty" url:"promptVersion,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (u *UpdateGenerationBody) GetTraceId() *string {
	if u == nil {
		return nil
	}
	return u.TraceId
}

func (u *UpdateGenerationBody) GetName() *string {
	if u == nil {
		return nil
	}
	return u.Name
}

func (u *UpdateGenerationBody) GetStartTime() *time.Time {
	if u == nil {
		return nil
	}
	return u.StartTime
}

func (u *UpdateGenerationBody) GetMetadata() interface{} {
	if u == nil {
		return nil
	}
	return u.Metadata
}

func (u *UpdateGenerationBody) GetInput() interface{} {
	if u == nil {
		return nil
	}
	return u.Input
}

func (u *UpdateGenerationBody) GetOutput() interface{} {
	if u == nil {
		return nil
	}
	return u.Output
}

func (u *UpdateGenerationBody) GetLevel() *ObservationLevel {
	if u == nil {
		return nil
	}
	return u.Level
}

func (u *UpdateGenerationBody) GetStatusMessage() *string {
	if u == nil {
		return nil
	}
	return u.StatusMessage
}

func (u *UpdateGenerationBody) GetParentObservationId() *string {
	if u == nil {
		return nil
	}
	return u.ParentObservationId
}

func (u *UpdateGenerationBody) GetVersion() *string {
	if u == nil {
		return nil
	}
	return u.Version
}

func (u *UpdateGenerationBody) GetId() string {
	if u == nil {
		return ""
	}
	return u.Id
}

func (u *UpdateGenerationBody) GetEndTime() *time.Time {
	if u == nil {
		return nil
	}
	return u.EndTime
}

func (u *UpdateGenerationBody) GetCompletionStartTime() *time.Time {
	if u == nil {
		return nil
	}
	return u.CompletionStartTime
}

func (u *UpdateGenerationBody) GetModel() *string {
	if u == nil {
		return nil
	}
	return u.Model
}

func (u *UpdateGenerationBody) GetModelParameters() map[string]*MapValue {
	if u == nil {
		return nil
	}
	return u.ModelParameters
}

func (u *UpdateGenerationBody) GetUsage() *IngestionUsage {
	if u == nil {
		return nil
	}
	return u.Usage
}

func (u *UpdateGenerationBody) GetPromptName() *string {
	if u == nil {
		return nil
	}
	return u.PromptName
}

func (u *UpdateGenerationBody) GetPromptVersion() *int {
	if u == nil {
		return nil
	}
	return u.PromptVersion
}

func (u *UpdateGenerationBody) GetExtraProperties() map[string]interface{} {
	return u.extraProperties
}

func (u *UpdateGenerationBody) UnmarshalJSON(data []byte) error {
	type embed UpdateGenerationBody
	var unmarshaler = struct {
		embed
		StartTime           *internal.DateTime `json:"startTime,omitempty"`
		EndTime             *internal.DateTime `json:"endTime,omitempty"`
		CompletionStartTime *internal.DateTime `json:"completionStartTime,omitempty"`
	}{
		embed: embed(*u),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*u = UpdateGenerationBody(unmarshaler.embed)
	u.StartTime = unmarshaler.StartTime.TimePtr()
	u.EndTime = unmarshaler.EndTime.TimePtr()
	u.CompletionStartTime = unmarshaler.CompletionStartTime.TimePtr()
	extraProperties, err := internal.ExtractExtraProperties(data, *u)
	if err != nil {
		return err
	}
	u.extraProperties = extraProperties
	u.rawJSON = json.RawMessage(data)
	return nil
}

func (u *UpdateGenerationBody) MarshalJSON() ([]byte, error) {
	type embed UpdateGenerationBody
	var marshaler = struct {
		embed
		StartTime           *internal.DateTime `json:"startTime,omitempty"`
		EndTime             *internal.DateTime `json:"endTime,omitempty"`
		CompletionStartTime *internal.DateTime `json:"completionStartTime,omitempty"`
	}{
		embed:               embed(*u),
		StartTime:           internal.NewOptionalDateTime(u.StartTime),
		EndTime:             internal.NewOptionalDateTime(u.EndTime),
		CompletionStartTime: internal.NewOptionalDateTime(u.CompletionStartTime),
	}
	return json.Marshal(marshaler)
}

func (u *UpdateGenerationBody) String() string {
	if len(u.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(u.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(u); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", u)
}

type UpdateGenerationEvent struct {
	// UUID v4 that identifies the event
	Id string `json:"id" url:"id"`
	// Datetime (ISO 8601) of event creation in client. Should be as close to actual event creation in client as possible, this timestamp will be used for ordering of events in future release. Resolution: milliseconds (required), microseconds (optimal).
	Timestamp string                `json:"timestamp" url:"timestamp"`
	Metadata  interface{}           `json:"metadata,omitempty" url:"metadata,omitempty"`
	Body      *UpdateGenerationBody `json:"body,omitempty" url:"body,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (u *UpdateGenerationEvent) GetId() string {
	if u == nil {
		return ""
	}
	return u.Id
}

func (u *UpdateGenerationEvent) GetTimestamp() string {
	if u == nil {
		return ""
	}
	return u.Timestamp
}

func (u *UpdateGenerationEvent) GetMetadata() interface{} {
	if u == nil {
		return nil
	}
	return u.Metadata
}

func (u *UpdateGenerationEvent) GetBody() *UpdateGenerationBody {
	if u == nil {
		return nil
	}
	return u.Body
}

func (u *UpdateGenerationEvent) GetExtraProperties() map[string]interface{} {
	return u.extraProperties
}

func (u *UpdateGenerationEvent) UnmarshalJSON(data []byte) error {
	type unmarshaler UpdateGenerationEvent
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*u = UpdateGenerationEvent(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *u)
	if err != nil {
		return err
	}
	u.extraProperties = extraProperties
	u.rawJSON = json.RawMessage(data)
	return nil
}

func (u *UpdateGenerationEvent) String() string {
	if len(u.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(u.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(u); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", u)
}

type UpdateObservationEvent struct {
	// UUID v4 that identifies the event
	Id string `json:"id" url:"id"`
	// Datetime (ISO 8601) of event creation in client. Should be as close to actual event creation in client as possible, this timestamp will be used for ordering of events in future release. Resolution: milliseconds (required), microseconds (optimal).
	Timestamp string           `json:"timestamp" url:"timestamp"`
	Metadata  interface{}      `json:"metadata,omitempty" url:"metadata,omitempty"`
	Body      *ObservationBody `json:"body,omitempty" url:"body,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (u *UpdateObservationEvent) GetId() string {
	if u == nil {
		return ""
	}
	return u.Id
}

func (u *UpdateObservationEvent) GetTimestamp() string {
	if u == nil {
		return ""
	}
	return u.Timestamp
}

func (u *UpdateObservationEvent) GetMetadata() interface{} {
	if u == nil {
		return nil
	}
	return u.Metadata
}

func (u *UpdateObservationEvent) GetBody() *ObservationBody {
	if u == nil {
		return nil
	}
	return u.Body
}

func (u *UpdateObservationEvent) GetExtraProperties() map[string]interface{} {
	return u.extraProperties
}

func (u *UpdateObservationEvent) UnmarshalJSON(data []byte) error {
	type unmarshaler UpdateObservationEvent
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*u = UpdateObservationEvent(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *u)
	if err != nil {
		return err
	}
	u.extraProperties = extraProperties
	u.rawJSON = json.RawMessage(data)
	return nil
}

func (u *UpdateObservationEvent) String() string {
	if len(u.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(u.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(u); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", u)
}

type UpdateSpanBody struct {
	TraceId             *string           `json:"traceId,omitempty" url:"traceId,omitempty"`
	Name                *string           `json:"name,omitempty" url:"name,omitempty"`
	StartTime           *time.Time        `json:"startTime,omitempty" url:"startTime,omitempty"`
	Metadata            interface{}       `json:"metadata,omitempty" url:"metadata,omitempty"`
	Input               interface{}       `json:"input,omitempty" url:"input,omitempty"`
	Output              interface{}       `json:"output,omitempty" url:"output,omitempty"`
	Level               *ObservationLevel `json:"level,omitempty" url:"level,omitempty"`
	StatusMessage       *string           `json:"statusMessage,omitempty" url:"statusMessage,omitempty"`
	ParentObservationId *string           `json:"parentObservationId,omitempty" url:"parentObservationId,omitempty"`
	Version             *string           `json:"version,omitempty" url:"version,omitempty"`
	Id                  string            `json:"id" url:"id"`
	EndTime             *time.Time        `json:"endTime,omitempty" url:"endTime,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (u *UpdateSpanBody) GetTraceId() *string {
	if u == nil {
		return nil
	}
	return u.TraceId
}

func (u *UpdateSpanBody) GetName() *string {
	if u == nil {
		return nil
	}
	return u.Name
}

func (u *UpdateSpanBody) GetStartTime() *time.Time {
	if u == nil {
		return nil
	}
	return u.StartTime
}

func (u *UpdateSpanBody) GetMetadata() interface{} {
	if u == nil {
		return nil
	}
	return u.Metadata
}

func (u *UpdateSpanBody) GetInput() interface{} {
	if u == nil {
		return nil
	}
	return u.Input
}

func (u *UpdateSpanBody) GetOutput() interface{} {
	if u == nil {
		return nil
	}
	return u.Output
}

func (u *UpdateSpanBody) GetLevel() *ObservationLevel {
	if u == nil {
		return nil
	}
	return u.Level
}

func (u *UpdateSpanBody) GetStatusMessage() *string {
	if u == nil {
		return nil
	}
	return u.StatusMessage
}

func (u *UpdateSpanBody) GetParentObservationId() *string {
	if u == nil {
		return nil
	}
	return u.ParentObservationId
}

func (u *UpdateSpanBody) GetVersion() *string {
	if u == nil {
		return nil
	}
	return u.Version
}

func (u *UpdateSpanBody) GetId() string {
	if u == nil {
		return ""
	}
	return u.Id
}

func (u *UpdateSpanBody) GetEndTime() *time.Time {
	if u == nil {
		return nil
	}
	return u.EndTime
}

func (u *UpdateSpanBody) GetExtraProperties() map[string]interface{} {
	return u.extraProperties
}

func (u *UpdateSpanBody) UnmarshalJSON(data []byte) error {
	type embed UpdateSpanBody
	var unmarshaler = struct {
		embed
		StartTime *internal.DateTime `json:"startTime,omitempty"`
		EndTime   *internal.DateTime `json:"endTime,omitempty"`
	}{
		embed: embed(*u),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*u = UpdateSpanBody(unmarshaler.embed)
	u.StartTime = unmarshaler.StartTime.TimePtr()
	u.EndTime = unmarshaler.EndTime.TimePtr()
	extraProperties, err := internal.ExtractExtraProperties(data, *u)
	if err != nil {
		return err
	}
	u.extraProperties = extraProperties
	u.rawJSON = json.RawMessage(data)
	return nil
}

func (u *UpdateSpanBody) MarshalJSON() ([]byte, error) {
	type embed UpdateSpanBody
	var marshaler = struct {
		embed
		StartTime *internal.DateTime `json:"startTime,omitempty"`
		EndTime   *internal.DateTime `json:"endTime,omitempty"`
	}{
		embed:     embed(*u),
		StartTime: internal.NewOptionalDateTime(u.StartTime),
		EndTime:   internal.NewOptionalDateTime(u.EndTime),
	}
	return json.Marshal(marshaler)
}

func (u *UpdateSpanBody) String() string {
	if len(u.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(u.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(u); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", u)
}

type UpdateSpanEvent struct {
	// UUID v4 that identifies the event
	Id string `json:"id" url:"id"`
	// Datetime (ISO 8601) of event creation in client. Should be as close to actual event creation in client as possible, this timestamp will be used for ordering of events in future release. Resolution: milliseconds (required), microseconds (optimal).
	Timestamp string          `json:"timestamp" url:"timestamp"`
	Metadata  interface{}     `json:"metadata,omitempty" url:"metadata,omitempty"`
	Body      *UpdateSpanBody `json:"body,omitempty" url:"body,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (u *UpdateSpanEvent) GetId() string {
	if u == nil {
		return ""
	}
	return u.Id
}

func (u *UpdateSpanEvent) GetTimestamp() string {
	if u == nil {
		return ""
	}
	return u.Timestamp
}

func (u *UpdateSpanEvent) GetMetadata() interface{} {
	if u == nil {
		return nil
	}
	return u.Metadata
}

func (u *UpdateSpanEvent) GetBody() *UpdateSpanBody {
	if u == nil {
		return nil
	}
	return u.Body
}

func (u *UpdateSpanEvent) GetExtraProperties() map[string]interface{} {
	return u.extraProperties
}

func (u *UpdateSpanEvent) UnmarshalJSON(data []byte) error {
	type unmarshaler UpdateSpanEvent
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*u = UpdateSpanEvent(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *u)
	if err != nil {
		return err
	}
	u.extraProperties = extraProperties
	u.rawJSON = json.RawMessage(data)
	return nil
}

func (u *UpdateSpanEvent) String() string {
	if len(u.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(u.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(u); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", u)
}
