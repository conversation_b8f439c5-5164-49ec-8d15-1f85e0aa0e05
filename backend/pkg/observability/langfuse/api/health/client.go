// This file was auto-generated by Fern from our API Definition.

package health

import (
	context "context"
	http "net/http"
	api "pentagi/pkg/observability/langfuse/api"
	core "pentagi/pkg/observability/langfuse/api/core"
	internal "pentagi/pkg/observability/langfuse/api/internal"
	option "pentagi/pkg/observability/langfuse/api/option"
)

type Client struct {
	baseURL string
	caller  *internal.Caller
	header  http.Header
}

func NewClient(opts ...option.RequestOption) *Client {
	options := core.NewRequestOptions(opts...)
	return &Client{
		baseURL: options.BaseURL,
		caller: internal.NewCaller(
			&internal.CallerParams{
				Client:      options.HTTPClient,
				MaxAttempts: options.MaxAttempts,
			},
		),
		header: options.ToHeader(),
	}
}

// Check health of API and database
func (c *Client) Health(
	ctx context.Context,
	opts ...option.RequestOption,
) (*api.HealthResponse, error) {
	options := core.NewRequestOptions(opts...)
	baseURL := internal.ResolveBaseURL(
		options.BaseURL,
		c.baseURL,
		"",
	)
	endpointURL := baseURL + "/api/public/health"
	headers := internal.MergeHeaders(
		c.header.Clone(),
		options.ToHeader(),
	)
	errorCodes := internal.ErrorCodes{
		400: func(apiError *core.APIError) error {
			return &api.BadRequestError{
				APIError: apiError,
			}
		},
		401: func(apiError *core.APIError) error {
			return &api.UnauthorizedError{
				APIError: apiError,
			}
		},
		403: func(apiError *core.APIError) error {
			return &api.ForbiddenError{
				APIError: apiError,
			}
		},
		404: func(apiError *core.APIError) error {
			return &api.NotFoundError{
				APIError: apiError,
			}
		},
		405: func(apiError *core.APIError) error {
			return &api.MethodNotAllowedError{
				APIError: apiError,
			}
		},
		503: func(apiError *core.APIError) error {
			return &api.ServiceUnavailableError{
				APIError: apiError,
			}
		},
	}

	var response *api.HealthResponse
	if err := c.caller.Call(
		ctx,
		&internal.CallParams{
			URL:             endpointURL,
			Method:          http.MethodGet,
			Headers:         headers,
			MaxAttempts:     options.MaxAttempts,
			BodyProperties:  options.BodyProperties,
			QueryParameters: options.QueryParameters,
			Client:          options.HTTPClient,
			Response:        &response,
			ErrorDecoder:    internal.NewErrorDecoder(errorCodes),
		},
	); err != nil {
		return nil, err
	}
	return response, nil
}
