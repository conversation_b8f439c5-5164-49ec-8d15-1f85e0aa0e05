// This file was auto-generated by <PERSON><PERSON> from our API Definition.

package api

import (
	json "encoding/json"
	fmt "fmt"
	internal "pentagi/pkg/observability/langfuse/api/internal"
	time "time"
)

type CreateScoreConfigRequest struct {
	Name     string        `json:"name" url:"-"`
	DataType ScoreDataType `json:"dataType" url:"-"`
	// Configure custom categories for categorical scores. Pass a list of objects with `label` and `value` properties. Categories are autogenerated for boolean configs and cannot be passed
	Categories []*ConfigCategory `json:"categories,omitempty" url:"-"`
	// Configure a minimum value for numerical scores. If not set, the minimum value defaults to -∞
	MinValue *float64 `json:"minValue,omitempty" url:"-"`
	// Configure a maximum value for numerical scores. If not set, the maximum value defaults to +∞
	MaxValue *float64 `json:"maxValue,omitempty" url:"-"`
	// Description is shown across the Langfuse UI and can be used to e.g. explain the config categories in detail, why a numeric range was set, or provide additional context on config name or usage
	Description *string `json:"description,omitempty" url:"-"`
}

type ScoreConfigsGetRequest struct {
	// Page number, starts at 1.
	Page *int `json:"-" url:"page,omitempty"`
	// Limit of items per page. If you encounter api issues due to too large page sizes, try to reduce the limit
	Limit *int `json:"-" url:"limit,omitempty"`
}

type ConfigCategory struct {
	Value float64 `json:"value" url:"value"`
	Label string  `json:"label" url:"label"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (c *ConfigCategory) GetValue() float64 {
	if c == nil {
		return 0
	}
	return c.Value
}

func (c *ConfigCategory) GetLabel() string {
	if c == nil {
		return ""
	}
	return c.Label
}

func (c *ConfigCategory) GetExtraProperties() map[string]interface{} {
	return c.extraProperties
}

func (c *ConfigCategory) UnmarshalJSON(data []byte) error {
	type unmarshaler ConfigCategory
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*c = ConfigCategory(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *c)
	if err != nil {
		return err
	}
	c.extraProperties = extraProperties
	c.rawJSON = json.RawMessage(data)
	return nil
}

func (c *ConfigCategory) String() string {
	if len(c.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(c.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(c); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", c)
}

// Configuration for a score
type ScoreConfig struct {
	Id        string        `json:"id" url:"id"`
	Name      string        `json:"name" url:"name"`
	CreatedAt time.Time     `json:"createdAt" url:"createdAt"`
	UpdatedAt time.Time     `json:"updatedAt" url:"updatedAt"`
	ProjectId string        `json:"projectId" url:"projectId"`
	DataType  ScoreDataType `json:"dataType" url:"dataType"`
	// Whether the score config is archived. Defaults to false
	IsArchived bool `json:"isArchived" url:"isArchived"`
	// Sets minimum value for numerical scores. If not set, the minimum value defaults to -∞
	MinValue *float64 `json:"minValue,omitempty" url:"minValue,omitempty"`
	// Sets maximum value for numerical scores. If not set, the maximum value defaults to +∞
	MaxValue *float64 `json:"maxValue,omitempty" url:"maxValue,omitempty"`
	// Configures custom categories for categorical scores
	Categories  []*ConfigCategory `json:"categories,omitempty" url:"categories,omitempty"`
	Description *string           `json:"description,omitempty" url:"description,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (s *ScoreConfig) GetId() string {
	if s == nil {
		return ""
	}
	return s.Id
}

func (s *ScoreConfig) GetName() string {
	if s == nil {
		return ""
	}
	return s.Name
}

func (s *ScoreConfig) GetCreatedAt() time.Time {
	if s == nil {
		return time.Time{}
	}
	return s.CreatedAt
}

func (s *ScoreConfig) GetUpdatedAt() time.Time {
	if s == nil {
		return time.Time{}
	}
	return s.UpdatedAt
}

func (s *ScoreConfig) GetProjectId() string {
	if s == nil {
		return ""
	}
	return s.ProjectId
}

func (s *ScoreConfig) GetDataType() ScoreDataType {
	if s == nil {
		return ""
	}
	return s.DataType
}

func (s *ScoreConfig) GetIsArchived() bool {
	if s == nil {
		return false
	}
	return s.IsArchived
}

func (s *ScoreConfig) GetMinValue() *float64 {
	if s == nil {
		return nil
	}
	return s.MinValue
}

func (s *ScoreConfig) GetMaxValue() *float64 {
	if s == nil {
		return nil
	}
	return s.MaxValue
}

func (s *ScoreConfig) GetCategories() []*ConfigCategory {
	if s == nil {
		return nil
	}
	return s.Categories
}

func (s *ScoreConfig) GetDescription() *string {
	if s == nil {
		return nil
	}
	return s.Description
}

func (s *ScoreConfig) GetExtraProperties() map[string]interface{} {
	return s.extraProperties
}

func (s *ScoreConfig) UnmarshalJSON(data []byte) error {
	type embed ScoreConfig
	var unmarshaler = struct {
		embed
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed: embed(*s),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*s = ScoreConfig(unmarshaler.embed)
	s.CreatedAt = unmarshaler.CreatedAt.Time()
	s.UpdatedAt = unmarshaler.UpdatedAt.Time()
	extraProperties, err := internal.ExtractExtraProperties(data, *s)
	if err != nil {
		return err
	}
	s.extraProperties = extraProperties
	s.rawJSON = json.RawMessage(data)
	return nil
}

func (s *ScoreConfig) MarshalJSON() ([]byte, error) {
	type embed ScoreConfig
	var marshaler = struct {
		embed
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed:     embed(*s),
		CreatedAt: internal.NewDateTime(s.CreatedAt),
		UpdatedAt: internal.NewDateTime(s.UpdatedAt),
	}
	return json.Marshal(marshaler)
}

func (s *ScoreConfig) String() string {
	if len(s.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(s.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(s); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", s)
}

type ScoreConfigs struct {
	Data []*ScoreConfig     `json:"data,omitempty" url:"data,omitempty"`
	Meta *UtilsMetaResponse `json:"meta,omitempty" url:"meta,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (s *ScoreConfigs) GetData() []*ScoreConfig {
	if s == nil {
		return nil
	}
	return s.Data
}

func (s *ScoreConfigs) GetMeta() *UtilsMetaResponse {
	if s == nil {
		return nil
	}
	return s.Meta
}

func (s *ScoreConfigs) GetExtraProperties() map[string]interface{} {
	return s.extraProperties
}

func (s *ScoreConfigs) UnmarshalJSON(data []byte) error {
	type unmarshaler ScoreConfigs
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*s = ScoreConfigs(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *s)
	if err != nil {
		return err
	}
	s.extraProperties = extraProperties
	s.rawJSON = json.RawMessage(data)
	return nil
}

func (s *ScoreConfigs) String() string {
	if len(s.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(s.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(s); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", s)
}
