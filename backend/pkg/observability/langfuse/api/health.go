// This file was auto-generated by Fern from our API Definition.

package api

import (
	json "encoding/json"
	fmt "fmt"
	internal "pentagi/pkg/observability/langfuse/api/internal"
)

type HealthResponse struct {
	// Langfuse server version
	Version string `json:"version" url:"version"`
	Status  string `json:"status" url:"status"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (h *HealthResponse) GetVersion() string {
	if h == nil {
		return ""
	}
	return h.Version
}

func (h *HealthResponse) GetStatus() string {
	if h == nil {
		return ""
	}
	return h.Status
}

func (h *HealthResponse) GetExtraProperties() map[string]interface{} {
	return h.extraProperties
}

func (h *HealthResponse) UnmarshalJSON(data []byte) error {
	type unmarshaler HealthResponse
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*h = HealthResponse(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *h)
	if err != nil {
		return err
	}
	h.extraProperties = extraProperties
	h.rawJSON = json.RawMessage(data)
	return nil
}

func (h *HealthResponse) String() string {
	if len(h.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(h.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(h); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", h)
}
