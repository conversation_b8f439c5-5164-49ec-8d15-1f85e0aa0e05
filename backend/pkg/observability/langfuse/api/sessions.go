// This file was auto-generated by <PERSON><PERSON> from our API Definition.

package api

import (
	json "encoding/json"
	fmt "fmt"
	internal "pentagi/pkg/observability/langfuse/api/internal"
	time "time"
)

type SessionsListRequest struct {
	// Page number, starts at 1
	Page *int `json:"-" url:"page,omitempty"`
	// Limit of items per page. If you encounter api issues due to too large page sizes, try to reduce the limit.
	Limit *int `json:"-" url:"limit,omitempty"`
	// Optional filter to only include sessions created on or after a certain datetime (ISO 8601)
	FromTimestamp *time.Time `json:"-" url:"fromTimestamp,omitempty"`
	// Optional filter to only include sessions created before a certain datetime (ISO 8601)
	ToTimestamp *time.Time `json:"-" url:"toTimestamp,omitempty"`
}

type PaginatedSessions struct {
	Data []*Session         `json:"data,omitempty" url:"data,omitempty"`
	Meta *UtilsMetaResponse `json:"meta,omitempty" url:"meta,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (p *PaginatedSessions) GetData() []*Session {
	if p == nil {
		return nil
	}
	return p.Data
}

func (p *PaginatedSessions) GetMeta() *UtilsMetaResponse {
	if p == nil {
		return nil
	}
	return p.Meta
}

func (p *PaginatedSessions) GetExtraProperties() map[string]interface{} {
	return p.extraProperties
}

func (p *PaginatedSessions) UnmarshalJSON(data []byte) error {
	type unmarshaler PaginatedSessions
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*p = PaginatedSessions(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *p)
	if err != nil {
		return err
	}
	p.extraProperties = extraProperties
	p.rawJSON = json.RawMessage(data)
	return nil
}

func (p *PaginatedSessions) String() string {
	if len(p.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(p.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(p); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", p)
}

type Session struct {
	Id        string    `json:"id" url:"id"`
	CreatedAt time.Time `json:"createdAt" url:"createdAt"`
	ProjectId string    `json:"projectId" url:"projectId"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (s *Session) GetId() string {
	if s == nil {
		return ""
	}
	return s.Id
}

func (s *Session) GetCreatedAt() time.Time {
	if s == nil {
		return time.Time{}
	}
	return s.CreatedAt
}

func (s *Session) GetProjectId() string {
	if s == nil {
		return ""
	}
	return s.ProjectId
}

func (s *Session) GetExtraProperties() map[string]interface{} {
	return s.extraProperties
}

func (s *Session) UnmarshalJSON(data []byte) error {
	type embed Session
	var unmarshaler = struct {
		embed
		CreatedAt *internal.DateTime `json:"createdAt"`
	}{
		embed: embed(*s),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*s = Session(unmarshaler.embed)
	s.CreatedAt = unmarshaler.CreatedAt.Time()
	extraProperties, err := internal.ExtractExtraProperties(data, *s)
	if err != nil {
		return err
	}
	s.extraProperties = extraProperties
	s.rawJSON = json.RawMessage(data)
	return nil
}

func (s *Session) MarshalJSON() ([]byte, error) {
	type embed Session
	var marshaler = struct {
		embed
		CreatedAt *internal.DateTime `json:"createdAt"`
	}{
		embed:     embed(*s),
		CreatedAt: internal.NewDateTime(s.CreatedAt),
	}
	return json.Marshal(marshaler)
}

func (s *Session) String() string {
	if len(s.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(s.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(s); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", s)
}

type SessionWithTraces struct {
	Id        string    `json:"id" url:"id"`
	CreatedAt time.Time `json:"createdAt" url:"createdAt"`
	ProjectId string    `json:"projectId" url:"projectId"`
	Traces    []*Trace  `json:"traces,omitempty" url:"traces,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (s *SessionWithTraces) GetId() string {
	if s == nil {
		return ""
	}
	return s.Id
}

func (s *SessionWithTraces) GetCreatedAt() time.Time {
	if s == nil {
		return time.Time{}
	}
	return s.CreatedAt
}

func (s *SessionWithTraces) GetProjectId() string {
	if s == nil {
		return ""
	}
	return s.ProjectId
}

func (s *SessionWithTraces) GetTraces() []*Trace {
	if s == nil {
		return nil
	}
	return s.Traces
}

func (s *SessionWithTraces) GetExtraProperties() map[string]interface{} {
	return s.extraProperties
}

func (s *SessionWithTraces) UnmarshalJSON(data []byte) error {
	type embed SessionWithTraces
	var unmarshaler = struct {
		embed
		CreatedAt *internal.DateTime `json:"createdAt"`
	}{
		embed: embed(*s),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*s = SessionWithTraces(unmarshaler.embed)
	s.CreatedAt = unmarshaler.CreatedAt.Time()
	extraProperties, err := internal.ExtractExtraProperties(data, *s)
	if err != nil {
		return err
	}
	s.extraProperties = extraProperties
	s.rawJSON = json.RawMessage(data)
	return nil
}

func (s *SessionWithTraces) MarshalJSON() ([]byte, error) {
	type embed SessionWithTraces
	var marshaler = struct {
		embed
		CreatedAt *internal.DateTime `json:"createdAt"`
	}{
		embed:     embed(*s),
		CreatedAt: internal.NewDateTime(s.CreatedAt),
	}
	return json.Marshal(marshaler)
}

func (s *SessionWithTraces) String() string {
	if len(s.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(s.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(s); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", s)
}
