// This file was auto-generated by <PERSON><PERSON> from our API Definition.

package api

import (
	json "encoding/json"
	fmt "fmt"
	internal "pentagi/pkg/observability/langfuse/api/internal"
	time "time"
)

type ObservationsGetManyRequest struct {
	// Page number, starts at 1.
	Page *int `json:"-" url:"page,omitempty"`
	// Limit of items per page. If you encounter api issues due to too large page sizes, try to reduce the limit.
	Limit               *int    `json:"-" url:"limit,omitempty"`
	Name                *string `json:"-" url:"name,omitempty"`
	UserId              *string `json:"-" url:"userId,omitempty"`
	Type                *string `json:"-" url:"type,omitempty"`
	TraceId             *string `json:"-" url:"traceId,omitempty"`
	ParentObservationId *string `json:"-" url:"parentObservationId,omitempty"`
	// Retrieve only observations with a start_time or or after this datetime (ISO 8601).
	FromStartTime *time.Time `json:"-" url:"fromStartTime,omitempty"`
	// Retrieve only observations with a start_time before this datetime (ISO 8601).
	ToStartTime *time.Time `json:"-" url:"toStartTime,omitempty"`
	// Optional filter to only include observations with a certain version.
	Version *string `json:"-" url:"version,omitempty"`
}

type ObservationsViews struct {
	Data []*ObservationsView `json:"data,omitempty" url:"data,omitempty"`
	Meta *UtilsMetaResponse  `json:"meta,omitempty" url:"meta,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (o *ObservationsViews) GetData() []*ObservationsView {
	if o == nil {
		return nil
	}
	return o.Data
}

func (o *ObservationsViews) GetMeta() *UtilsMetaResponse {
	if o == nil {
		return nil
	}
	return o.Meta
}

func (o *ObservationsViews) GetExtraProperties() map[string]interface{} {
	return o.extraProperties
}

func (o *ObservationsViews) UnmarshalJSON(data []byte) error {
	type unmarshaler ObservationsViews
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*o = ObservationsViews(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *o)
	if err != nil {
		return err
	}
	o.extraProperties = extraProperties
	o.rawJSON = json.RawMessage(data)
	return nil
}

func (o *ObservationsViews) String() string {
	if len(o.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(o.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(o); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", o)
}
