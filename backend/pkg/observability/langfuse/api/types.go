// This file was auto-generated by Fe<PERSON> from our API Definition.

package api

import (
	json "encoding/json"
	fmt "fmt"
	internal "pentagi/pkg/observability/langfuse/api/internal"
	time "time"
)

type BaseScore struct {
	Id            string      `json:"id" url:"id"`
	TraceId       string      `json:"traceId" url:"traceId"`
	Name          string      `json:"name" url:"name"`
	Source        ScoreSource `json:"source" url:"source"`
	ObservationId *string     `json:"observationId,omitempty" url:"observationId,omitempty"`
	Timestamp     time.Time   `json:"timestamp" url:"timestamp"`
	CreatedAt     time.Time   `json:"createdAt" url:"createdAt"`
	UpdatedAt     time.Time   `json:"updatedAt" url:"updatedAt"`
	AuthorUserId  *string     `json:"authorUserId,omitempty" url:"authorUserId,omitempty"`
	Comment       *string     `json:"comment,omitempty" url:"comment,omitempty"`
	// Reference a score config on a score. When set, config and score name must be equal and value must comply to optionally defined numerical range
	ConfigId *string `json:"configId,omitempty" url:"configId,omitempty"`
	// Reference an annotation queue on a score. Populated if the score was initially created in an annotation queue.
	QueueId *string `json:"queueId,omitempty" url:"queueId,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (b *BaseScore) GetId() string {
	if b == nil {
		return ""
	}
	return b.Id
}

func (b *BaseScore) GetTraceId() string {
	if b == nil {
		return ""
	}
	return b.TraceId
}

func (b *BaseScore) GetName() string {
	if b == nil {
		return ""
	}
	return b.Name
}

func (b *BaseScore) GetSource() ScoreSource {
	if b == nil {
		return ""
	}
	return b.Source
}

func (b *BaseScore) GetObservationId() *string {
	if b == nil {
		return nil
	}
	return b.ObservationId
}

func (b *BaseScore) GetTimestamp() time.Time {
	if b == nil {
		return time.Time{}
	}
	return b.Timestamp
}

func (b *BaseScore) GetCreatedAt() time.Time {
	if b == nil {
		return time.Time{}
	}
	return b.CreatedAt
}

func (b *BaseScore) GetUpdatedAt() time.Time {
	if b == nil {
		return time.Time{}
	}
	return b.UpdatedAt
}

func (b *BaseScore) GetAuthorUserId() *string {
	if b == nil {
		return nil
	}
	return b.AuthorUserId
}

func (b *BaseScore) GetComment() *string {
	if b == nil {
		return nil
	}
	return b.Comment
}

func (b *BaseScore) GetConfigId() *string {
	if b == nil {
		return nil
	}
	return b.ConfigId
}

func (b *BaseScore) GetQueueId() *string {
	if b == nil {
		return nil
	}
	return b.QueueId
}

func (b *BaseScore) GetExtraProperties() map[string]interface{} {
	return b.extraProperties
}

func (b *BaseScore) UnmarshalJSON(data []byte) error {
	type embed BaseScore
	var unmarshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed: embed(*b),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*b = BaseScore(unmarshaler.embed)
	b.Timestamp = unmarshaler.Timestamp.Time()
	b.CreatedAt = unmarshaler.CreatedAt.Time()
	b.UpdatedAt = unmarshaler.UpdatedAt.Time()
	extraProperties, err := internal.ExtractExtraProperties(data, *b)
	if err != nil {
		return err
	}
	b.extraProperties = extraProperties
	b.rawJSON = json.RawMessage(data)
	return nil
}

func (b *BaseScore) MarshalJSON() ([]byte, error) {
	type embed BaseScore
	var marshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed:     embed(*b),
		Timestamp: internal.NewDateTime(b.Timestamp),
		CreatedAt: internal.NewDateTime(b.CreatedAt),
		UpdatedAt: internal.NewDateTime(b.UpdatedAt),
	}
	return json.Marshal(marshaler)
}

func (b *BaseScore) String() string {
	if len(b.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(b.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(b); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", b)
}

type BooleanScore struct {
	Id            string      `json:"id" url:"id"`
	TraceId       string      `json:"traceId" url:"traceId"`
	Name          string      `json:"name" url:"name"`
	Source        ScoreSource `json:"source" url:"source"`
	ObservationId *string     `json:"observationId,omitempty" url:"observationId,omitempty"`
	Timestamp     time.Time   `json:"timestamp" url:"timestamp"`
	CreatedAt     time.Time   `json:"createdAt" url:"createdAt"`
	UpdatedAt     time.Time   `json:"updatedAt" url:"updatedAt"`
	AuthorUserId  *string     `json:"authorUserId,omitempty" url:"authorUserId,omitempty"`
	Comment       *string     `json:"comment,omitempty" url:"comment,omitempty"`
	// Reference a score config on a score. When set, config and score name must be equal and value must comply to optionally defined numerical range
	ConfigId *string `json:"configId,omitempty" url:"configId,omitempty"`
	// Reference an annotation queue on a score. Populated if the score was initially created in an annotation queue.
	QueueId *string `json:"queueId,omitempty" url:"queueId,omitempty"`
	// The numeric value of the score. Equals 1 for "True" and 0 for "False"
	Value float64 `json:"value" url:"value"`
	// The string representation of the score value. Is inferred from the numeric value and equals "True" or "False"
	StringValue string `json:"stringValue" url:"stringValue"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (b *BooleanScore) GetId() string {
	if b == nil {
		return ""
	}
	return b.Id
}

func (b *BooleanScore) GetTraceId() string {
	if b == nil {
		return ""
	}
	return b.TraceId
}

func (b *BooleanScore) GetName() string {
	if b == nil {
		return ""
	}
	return b.Name
}

func (b *BooleanScore) GetSource() ScoreSource {
	if b == nil {
		return ""
	}
	return b.Source
}

func (b *BooleanScore) GetObservationId() *string {
	if b == nil {
		return nil
	}
	return b.ObservationId
}

func (b *BooleanScore) GetTimestamp() time.Time {
	if b == nil {
		return time.Time{}
	}
	return b.Timestamp
}

func (b *BooleanScore) GetCreatedAt() time.Time {
	if b == nil {
		return time.Time{}
	}
	return b.CreatedAt
}

func (b *BooleanScore) GetUpdatedAt() time.Time {
	if b == nil {
		return time.Time{}
	}
	return b.UpdatedAt
}

func (b *BooleanScore) GetAuthorUserId() *string {
	if b == nil {
		return nil
	}
	return b.AuthorUserId
}

func (b *BooleanScore) GetComment() *string {
	if b == nil {
		return nil
	}
	return b.Comment
}

func (b *BooleanScore) GetConfigId() *string {
	if b == nil {
		return nil
	}
	return b.ConfigId
}

func (b *BooleanScore) GetQueueId() *string {
	if b == nil {
		return nil
	}
	return b.QueueId
}

func (b *BooleanScore) GetValue() float64 {
	if b == nil {
		return 0
	}
	return b.Value
}

func (b *BooleanScore) GetStringValue() string {
	if b == nil {
		return ""
	}
	return b.StringValue
}

func (b *BooleanScore) GetExtraProperties() map[string]interface{} {
	return b.extraProperties
}

func (b *BooleanScore) UnmarshalJSON(data []byte) error {
	type embed BooleanScore
	var unmarshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed: embed(*b),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*b = BooleanScore(unmarshaler.embed)
	b.Timestamp = unmarshaler.Timestamp.Time()
	b.CreatedAt = unmarshaler.CreatedAt.Time()
	b.UpdatedAt = unmarshaler.UpdatedAt.Time()
	extraProperties, err := internal.ExtractExtraProperties(data, *b)
	if err != nil {
		return err
	}
	b.extraProperties = extraProperties
	b.rawJSON = json.RawMessage(data)
	return nil
}

func (b *BooleanScore) MarshalJSON() ([]byte, error) {
	type embed BooleanScore
	var marshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed:     embed(*b),
		Timestamp: internal.NewDateTime(b.Timestamp),
		CreatedAt: internal.NewDateTime(b.CreatedAt),
		UpdatedAt: internal.NewDateTime(b.UpdatedAt),
	}
	return json.Marshal(marshaler)
}

func (b *BooleanScore) String() string {
	if len(b.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(b.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(b); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", b)
}

type CategoricalScore struct {
	Id            string      `json:"id" url:"id"`
	TraceId       string      `json:"traceId" url:"traceId"`
	Name          string      `json:"name" url:"name"`
	Source        ScoreSource `json:"source" url:"source"`
	ObservationId *string     `json:"observationId,omitempty" url:"observationId,omitempty"`
	Timestamp     time.Time   `json:"timestamp" url:"timestamp"`
	CreatedAt     time.Time   `json:"createdAt" url:"createdAt"`
	UpdatedAt     time.Time   `json:"updatedAt" url:"updatedAt"`
	AuthorUserId  *string     `json:"authorUserId,omitempty" url:"authorUserId,omitempty"`
	Comment       *string     `json:"comment,omitempty" url:"comment,omitempty"`
	// Reference a score config on a score. When set, config and score name must be equal and value must comply to optionally defined numerical range
	ConfigId *string `json:"configId,omitempty" url:"configId,omitempty"`
	// Reference an annotation queue on a score. Populated if the score was initially created in an annotation queue.
	QueueId *string `json:"queueId,omitempty" url:"queueId,omitempty"`
	// Only defined if a config is linked. Represents the numeric category mapping of the stringValue
	Value *float64 `json:"value,omitempty" url:"value,omitempty"`
	// The string representation of the score value. If no config is linked, can be any string. Otherwise, must map to a config category
	StringValue string `json:"stringValue" url:"stringValue"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (c *CategoricalScore) GetId() string {
	if c == nil {
		return ""
	}
	return c.Id
}

func (c *CategoricalScore) GetTraceId() string {
	if c == nil {
		return ""
	}
	return c.TraceId
}

func (c *CategoricalScore) GetName() string {
	if c == nil {
		return ""
	}
	return c.Name
}

func (c *CategoricalScore) GetSource() ScoreSource {
	if c == nil {
		return ""
	}
	return c.Source
}

func (c *CategoricalScore) GetObservationId() *string {
	if c == nil {
		return nil
	}
	return c.ObservationId
}

func (c *CategoricalScore) GetTimestamp() time.Time {
	if c == nil {
		return time.Time{}
	}
	return c.Timestamp
}

func (c *CategoricalScore) GetCreatedAt() time.Time {
	if c == nil {
		return time.Time{}
	}
	return c.CreatedAt
}

func (c *CategoricalScore) GetUpdatedAt() time.Time {
	if c == nil {
		return time.Time{}
	}
	return c.UpdatedAt
}

func (c *CategoricalScore) GetAuthorUserId() *string {
	if c == nil {
		return nil
	}
	return c.AuthorUserId
}

func (c *CategoricalScore) GetComment() *string {
	if c == nil {
		return nil
	}
	return c.Comment
}

func (c *CategoricalScore) GetConfigId() *string {
	if c == nil {
		return nil
	}
	return c.ConfigId
}

func (c *CategoricalScore) GetQueueId() *string {
	if c == nil {
		return nil
	}
	return c.QueueId
}

func (c *CategoricalScore) GetValue() *float64 {
	if c == nil {
		return nil
	}
	return c.Value
}

func (c *CategoricalScore) GetStringValue() string {
	if c == nil {
		return ""
	}
	return c.StringValue
}

func (c *CategoricalScore) GetExtraProperties() map[string]interface{} {
	return c.extraProperties
}

func (c *CategoricalScore) UnmarshalJSON(data []byte) error {
	type embed CategoricalScore
	var unmarshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed: embed(*c),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*c = CategoricalScore(unmarshaler.embed)
	c.Timestamp = unmarshaler.Timestamp.Time()
	c.CreatedAt = unmarshaler.CreatedAt.Time()
	c.UpdatedAt = unmarshaler.UpdatedAt.Time()
	extraProperties, err := internal.ExtractExtraProperties(data, *c)
	if err != nil {
		return err
	}
	c.extraProperties = extraProperties
	c.rawJSON = json.RawMessage(data)
	return nil
}

func (c *CategoricalScore) MarshalJSON() ([]byte, error) {
	type embed CategoricalScore
	var marshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed:     embed(*c),
		Timestamp: internal.NewDateTime(c.Timestamp),
		CreatedAt: internal.NewDateTime(c.CreatedAt),
		UpdatedAt: internal.NewDateTime(c.UpdatedAt),
	}
	return json.Marshal(marshaler)
}

func (c *CategoricalScore) String() string {
	if len(c.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(c.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(c); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", c)
}

// The value of the score. Must be passed as string for categorical scores, and numeric for boolean and numeric scores
type CreateScoreValue struct {
	Double float64
	String string

	typ string
}

func NewCreateScoreValueFromDouble(value float64) *CreateScoreValue {
	return &CreateScoreValue{typ: "Double", Double: value}
}

func NewCreateScoreValueFromString(value string) *CreateScoreValue {
	return &CreateScoreValue{typ: "String", String: value}
}

func (c *CreateScoreValue) GetDouble() float64 {
	if c == nil {
		return 0
	}
	return c.Double
}

func (c *CreateScoreValue) GetString() string {
	if c == nil {
		return ""
	}
	return c.String
}

func (c *CreateScoreValue) UnmarshalJSON(data []byte) error {
	var valueDouble float64
	if err := json.Unmarshal(data, &valueDouble); err == nil {
		c.typ = "Double"
		c.Double = valueDouble
		return nil
	}
	var valueString string
	if err := json.Unmarshal(data, &valueString); err == nil {
		c.typ = "String"
		c.String = valueString
		return nil
	}
	return fmt.Errorf("%s cannot be deserialized as a %T", data, c)
}

func (c CreateScoreValue) MarshalJSON() ([]byte, error) {
	if c.typ == "Double" || c.Double != 0 {
		return json.Marshal(c.Double)
	}
	if c.typ == "String" || c.String != "" {
		return json.Marshal(c.String)
	}
	return nil, fmt.Errorf("type %T does not include a non-empty union type", c)
}

type CreateScoreValueVisitor interface {
	VisitDouble(float64) error
	VisitString(string) error
}

func (c *CreateScoreValue) Accept(visitor CreateScoreValueVisitor) error {
	if c.typ == "Double" || c.Double != 0 {
		return visitor.VisitDouble(c.Double)
	}
	if c.typ == "String" || c.String != "" {
		return visitor.VisitString(c.String)
	}
	return fmt.Errorf("type %T does not include a non-empty union type", c)
}

type DatasetRunItem struct {
	Id             string    `json:"id" url:"id"`
	DatasetRunId   string    `json:"datasetRunId" url:"datasetRunId"`
	DatasetRunName string    `json:"datasetRunName" url:"datasetRunName"`
	DatasetItemId  string    `json:"datasetItemId" url:"datasetItemId"`
	TraceId        string    `json:"traceId" url:"traceId"`
	ObservationId  *string   `json:"observationId,omitempty" url:"observationId,omitempty"`
	CreatedAt      time.Time `json:"createdAt" url:"createdAt"`
	UpdatedAt      time.Time `json:"updatedAt" url:"updatedAt"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (d *DatasetRunItem) GetId() string {
	if d == nil {
		return ""
	}
	return d.Id
}

func (d *DatasetRunItem) GetDatasetRunId() string {
	if d == nil {
		return ""
	}
	return d.DatasetRunId
}

func (d *DatasetRunItem) GetDatasetRunName() string {
	if d == nil {
		return ""
	}
	return d.DatasetRunName
}

func (d *DatasetRunItem) GetDatasetItemId() string {
	if d == nil {
		return ""
	}
	return d.DatasetItemId
}

func (d *DatasetRunItem) GetTraceId() string {
	if d == nil {
		return ""
	}
	return d.TraceId
}

func (d *DatasetRunItem) GetObservationId() *string {
	if d == nil {
		return nil
	}
	return d.ObservationId
}

func (d *DatasetRunItem) GetCreatedAt() time.Time {
	if d == nil {
		return time.Time{}
	}
	return d.CreatedAt
}

func (d *DatasetRunItem) GetUpdatedAt() time.Time {
	if d == nil {
		return time.Time{}
	}
	return d.UpdatedAt
}

func (d *DatasetRunItem) GetExtraProperties() map[string]interface{} {
	return d.extraProperties
}

func (d *DatasetRunItem) UnmarshalJSON(data []byte) error {
	type embed DatasetRunItem
	var unmarshaler = struct {
		embed
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed: embed(*d),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*d = DatasetRunItem(unmarshaler.embed)
	d.CreatedAt = unmarshaler.CreatedAt.Time()
	d.UpdatedAt = unmarshaler.UpdatedAt.Time()
	extraProperties, err := internal.ExtractExtraProperties(data, *d)
	if err != nil {
		return err
	}
	d.extraProperties = extraProperties
	d.rawJSON = json.RawMessage(data)
	return nil
}

func (d *DatasetRunItem) MarshalJSON() ([]byte, error) {
	type embed DatasetRunItem
	var marshaler = struct {
		embed
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed:     embed(*d),
		CreatedAt: internal.NewDateTime(d.CreatedAt),
		UpdatedAt: internal.NewDateTime(d.UpdatedAt),
	}
	return json.Marshal(marshaler)
}

func (d *DatasetRunItem) String() string {
	if len(d.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(d.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(d); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", d)
}

type MapValue struct {
	StringOptional     *string
	IntegerOptional    *int
	BooleanOptional    *bool
	StringListOptional []string

	typ string
}

func NewMapValueFromStringOptional(value *string) *MapValue {
	return &MapValue{typ: "StringOptional", StringOptional: value}
}

func NewMapValueFromIntegerOptional(value *int) *MapValue {
	return &MapValue{typ: "IntegerOptional", IntegerOptional: value}
}

func NewMapValueFromBooleanOptional(value *bool) *MapValue {
	return &MapValue{typ: "BooleanOptional", BooleanOptional: value}
}

func NewMapValueFromStringListOptional(value []string) *MapValue {
	return &MapValue{typ: "StringListOptional", StringListOptional: value}
}

func (m *MapValue) GetStringOptional() *string {
	if m == nil {
		return nil
	}
	return m.StringOptional
}

func (m *MapValue) GetIntegerOptional() *int {
	if m == nil {
		return nil
	}
	return m.IntegerOptional
}

func (m *MapValue) GetBooleanOptional() *bool {
	if m == nil {
		return nil
	}
	return m.BooleanOptional
}

func (m *MapValue) GetStringListOptional() []string {
	if m == nil {
		return nil
	}
	return m.StringListOptional
}

func (m *MapValue) UnmarshalJSON(data []byte) error {
	var valueStringOptional *string
	if err := json.Unmarshal(data, &valueStringOptional); err == nil {
		m.typ = "StringOptional"
		m.StringOptional = valueStringOptional
		return nil
	}
	var valueIntegerOptional *int
	if err := json.Unmarshal(data, &valueIntegerOptional); err == nil {
		m.typ = "IntegerOptional"
		m.IntegerOptional = valueIntegerOptional
		return nil
	}
	var valueBooleanOptional *bool
	if err := json.Unmarshal(data, &valueBooleanOptional); err == nil {
		m.typ = "BooleanOptional"
		m.BooleanOptional = valueBooleanOptional
		return nil
	}
	var valueStringListOptional []string
	if err := json.Unmarshal(data, &valueStringListOptional); err == nil {
		m.typ = "StringListOptional"
		m.StringListOptional = valueStringListOptional
		return nil
	}
	return fmt.Errorf("%s cannot be deserialized as a %T", data, m)
}

func (m MapValue) MarshalJSON() ([]byte, error) {
	if m.typ == "StringOptional" || m.StringOptional != nil {
		return json.Marshal(m.StringOptional)
	}
	if m.typ == "IntegerOptional" || m.IntegerOptional != nil {
		return json.Marshal(m.IntegerOptional)
	}
	if m.typ == "BooleanOptional" || m.BooleanOptional != nil {
		return json.Marshal(m.BooleanOptional)
	}
	if m.typ == "StringListOptional" || m.StringListOptional != nil {
		return json.Marshal(m.StringListOptional)
	}
	return nil, fmt.Errorf("type %T does not include a non-empty union type", m)
}

type MapValueVisitor interface {
	VisitStringOptional(*string) error
	VisitIntegerOptional(*int) error
	VisitBooleanOptional(*bool) error
	VisitStringListOptional([]string) error
}

func (m *MapValue) Accept(visitor MapValueVisitor) error {
	if m.typ == "StringOptional" || m.StringOptional != nil {
		return visitor.VisitStringOptional(m.StringOptional)
	}
	if m.typ == "IntegerOptional" || m.IntegerOptional != nil {
		return visitor.VisitIntegerOptional(m.IntegerOptional)
	}
	if m.typ == "BooleanOptional" || m.BooleanOptional != nil {
		return visitor.VisitBooleanOptional(m.BooleanOptional)
	}
	if m.typ == "StringListOptional" || m.StringListOptional != nil {
		return visitor.VisitStringListOptional(m.StringListOptional)
	}
	return fmt.Errorf("type %T does not include a non-empty union type", m)
}

// Unit of usage in Langfuse
type ModelUsageUnit string

const (
	ModelUsageUnitCharacters   ModelUsageUnit = "CHARACTERS"
	ModelUsageUnitTokens       ModelUsageUnit = "TOKENS"
	ModelUsageUnitMilliseconds ModelUsageUnit = "MILLISECONDS"
	ModelUsageUnitSeconds      ModelUsageUnit = "SECONDS"
	ModelUsageUnitImages       ModelUsageUnit = "IMAGES"
	ModelUsageUnitRequests     ModelUsageUnit = "REQUESTS"
)

func NewModelUsageUnitFromString(s string) (ModelUsageUnit, error) {
	switch s {
	case "CHARACTERS":
		return ModelUsageUnitCharacters, nil
	case "TOKENS":
		return ModelUsageUnitTokens, nil
	case "MILLISECONDS":
		return ModelUsageUnitMilliseconds, nil
	case "SECONDS":
		return ModelUsageUnitSeconds, nil
	case "IMAGES":
		return ModelUsageUnitImages, nil
	case "REQUESTS":
		return ModelUsageUnitRequests, nil
	}
	var t ModelUsageUnit
	return "", fmt.Errorf("%s is not a valid %T", s, t)
}

func (m ModelUsageUnit) Ptr() *ModelUsageUnit {
	return &m
}

type NumericScore struct {
	Id            string      `json:"id" url:"id"`
	TraceId       string      `json:"traceId" url:"traceId"`
	Name          string      `json:"name" url:"name"`
	Source        ScoreSource `json:"source" url:"source"`
	ObservationId *string     `json:"observationId,omitempty" url:"observationId,omitempty"`
	Timestamp     time.Time   `json:"timestamp" url:"timestamp"`
	CreatedAt     time.Time   `json:"createdAt" url:"createdAt"`
	UpdatedAt     time.Time   `json:"updatedAt" url:"updatedAt"`
	AuthorUserId  *string     `json:"authorUserId,omitempty" url:"authorUserId,omitempty"`
	Comment       *string     `json:"comment,omitempty" url:"comment,omitempty"`
	// Reference a score config on a score. When set, config and score name must be equal and value must comply to optionally defined numerical range
	ConfigId *string `json:"configId,omitempty" url:"configId,omitempty"`
	// Reference an annotation queue on a score. Populated if the score was initially created in an annotation queue.
	QueueId *string `json:"queueId,omitempty" url:"queueId,omitempty"`
	// The numeric value of the score
	Value float64 `json:"value" url:"value"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (n *NumericScore) GetId() string {
	if n == nil {
		return ""
	}
	return n.Id
}

func (n *NumericScore) GetTraceId() string {
	if n == nil {
		return ""
	}
	return n.TraceId
}

func (n *NumericScore) GetName() string {
	if n == nil {
		return ""
	}
	return n.Name
}

func (n *NumericScore) GetSource() ScoreSource {
	if n == nil {
		return ""
	}
	return n.Source
}

func (n *NumericScore) GetObservationId() *string {
	if n == nil {
		return nil
	}
	return n.ObservationId
}

func (n *NumericScore) GetTimestamp() time.Time {
	if n == nil {
		return time.Time{}
	}
	return n.Timestamp
}

func (n *NumericScore) GetCreatedAt() time.Time {
	if n == nil {
		return time.Time{}
	}
	return n.CreatedAt
}

func (n *NumericScore) GetUpdatedAt() time.Time {
	if n == nil {
		return time.Time{}
	}
	return n.UpdatedAt
}

func (n *NumericScore) GetAuthorUserId() *string {
	if n == nil {
		return nil
	}
	return n.AuthorUserId
}

func (n *NumericScore) GetComment() *string {
	if n == nil {
		return nil
	}
	return n.Comment
}

func (n *NumericScore) GetConfigId() *string {
	if n == nil {
		return nil
	}
	return n.ConfigId
}

func (n *NumericScore) GetQueueId() *string {
	if n == nil {
		return nil
	}
	return n.QueueId
}

func (n *NumericScore) GetValue() float64 {
	if n == nil {
		return 0
	}
	return n.Value
}

func (n *NumericScore) GetExtraProperties() map[string]interface{} {
	return n.extraProperties
}

func (n *NumericScore) UnmarshalJSON(data []byte) error {
	type embed NumericScore
	var unmarshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed: embed(*n),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*n = NumericScore(unmarshaler.embed)
	n.Timestamp = unmarshaler.Timestamp.Time()
	n.CreatedAt = unmarshaler.CreatedAt.Time()
	n.UpdatedAt = unmarshaler.UpdatedAt.Time()
	extraProperties, err := internal.ExtractExtraProperties(data, *n)
	if err != nil {
		return err
	}
	n.extraProperties = extraProperties
	n.rawJSON = json.RawMessage(data)
	return nil
}

func (n *NumericScore) MarshalJSON() ([]byte, error) {
	type embed NumericScore
	var marshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed:     embed(*n),
		Timestamp: internal.NewDateTime(n.Timestamp),
		CreatedAt: internal.NewDateTime(n.CreatedAt),
		UpdatedAt: internal.NewDateTime(n.UpdatedAt),
	}
	return json.Marshal(marshaler)
}

func (n *NumericScore) String() string {
	if len(n.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(n.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(n); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", n)
}

type Observation struct {
	// The unique identifier of the observation
	Id string `json:"id" url:"id"`
	// The trace ID associated with the observation
	TraceId *string `json:"traceId,omitempty" url:"traceId,omitempty"`
	// The type of the observation
	Type string `json:"type" url:"type"`
	// The name of the observation
	Name *string `json:"name,omitempty" url:"name,omitempty"`
	// The start time of the observation
	StartTime time.Time `json:"startTime" url:"startTime"`
	// The end time of the observation.
	EndTime *time.Time `json:"endTime,omitempty" url:"endTime,omitempty"`
	// The completion start time of the observation
	CompletionStartTime *time.Time `json:"completionStartTime,omitempty" url:"completionStartTime,omitempty"`
	// The model used for the observation
	Model *string `json:"model,omitempty" url:"model,omitempty"`
	// The parameters of the model used for the observation
	ModelParameters map[string]*MapValue `json:"modelParameters,omitempty" url:"modelParameters,omitempty"`
	Input           interface{}          `json:"input,omitempty" url:"input,omitempty"`
	// The version of the observation
	Version  *string     `json:"version,omitempty" url:"version,omitempty"`
	Metadata interface{} `json:"metadata,omitempty" url:"metadata,omitempty"`
	Output   interface{} `json:"output,omitempty" url:"output,omitempty"`
	// The usage data of the observation
	Usage *Usage `json:"usage,omitempty" url:"usage,omitempty"`
	// The level of the observation
	Level ObservationLevel `json:"level" url:"level"`
	// The status message of the observation
	StatusMessage *string `json:"statusMessage,omitempty" url:"statusMessage,omitempty"`
	// The parent observation ID
	ParentObservationId *string `json:"parentObservationId,omitempty" url:"parentObservationId,omitempty"`
	// The prompt ID associated with the observation
	PromptId *string `json:"promptId,omitempty" url:"promptId,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (o *Observation) GetId() string {
	if o == nil {
		return ""
	}
	return o.Id
}

func (o *Observation) GetTraceId() *string {
	if o == nil {
		return nil
	}
	return o.TraceId
}

func (o *Observation) GetType() string {
	if o == nil {
		return ""
	}
	return o.Type
}

func (o *Observation) GetName() *string {
	if o == nil {
		return nil
	}
	return o.Name
}

func (o *Observation) GetStartTime() time.Time {
	if o == nil {
		return time.Time{}
	}
	return o.StartTime
}

func (o *Observation) GetEndTime() *time.Time {
	if o == nil {
		return nil
	}
	return o.EndTime
}

func (o *Observation) GetCompletionStartTime() *time.Time {
	if o == nil {
		return nil
	}
	return o.CompletionStartTime
}

func (o *Observation) GetModel() *string {
	if o == nil {
		return nil
	}
	return o.Model
}

func (o *Observation) GetModelParameters() map[string]*MapValue {
	if o == nil {
		return nil
	}
	return o.ModelParameters
}

func (o *Observation) GetInput() interface{} {
	if o == nil {
		return nil
	}
	return o.Input
}

func (o *Observation) GetVersion() *string {
	if o == nil {
		return nil
	}
	return o.Version
}

func (o *Observation) GetMetadata() interface{} {
	if o == nil {
		return nil
	}
	return o.Metadata
}

func (o *Observation) GetOutput() interface{} {
	if o == nil {
		return nil
	}
	return o.Output
}

func (o *Observation) GetUsage() *Usage {
	if o == nil {
		return nil
	}
	return o.Usage
}

func (o *Observation) GetLevel() ObservationLevel {
	if o == nil {
		return ""
	}
	return o.Level
}

func (o *Observation) GetStatusMessage() *string {
	if o == nil {
		return nil
	}
	return o.StatusMessage
}

func (o *Observation) GetParentObservationId() *string {
	if o == nil {
		return nil
	}
	return o.ParentObservationId
}

func (o *Observation) GetPromptId() *string {
	if o == nil {
		return nil
	}
	return o.PromptId
}

func (o *Observation) GetExtraProperties() map[string]interface{} {
	return o.extraProperties
}

func (o *Observation) UnmarshalJSON(data []byte) error {
	type embed Observation
	var unmarshaler = struct {
		embed
		StartTime           *internal.DateTime `json:"startTime"`
		EndTime             *internal.DateTime `json:"endTime,omitempty"`
		CompletionStartTime *internal.DateTime `json:"completionStartTime,omitempty"`
	}{
		embed: embed(*o),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*o = Observation(unmarshaler.embed)
	o.StartTime = unmarshaler.StartTime.Time()
	o.EndTime = unmarshaler.EndTime.TimePtr()
	o.CompletionStartTime = unmarshaler.CompletionStartTime.TimePtr()
	extraProperties, err := internal.ExtractExtraProperties(data, *o)
	if err != nil {
		return err
	}
	o.extraProperties = extraProperties
	o.rawJSON = json.RawMessage(data)
	return nil
}

func (o *Observation) MarshalJSON() ([]byte, error) {
	type embed Observation
	var marshaler = struct {
		embed
		StartTime           *internal.DateTime `json:"startTime"`
		EndTime             *internal.DateTime `json:"endTime,omitempty"`
		CompletionStartTime *internal.DateTime `json:"completionStartTime,omitempty"`
	}{
		embed:               embed(*o),
		StartTime:           internal.NewDateTime(o.StartTime),
		EndTime:             internal.NewOptionalDateTime(o.EndTime),
		CompletionStartTime: internal.NewOptionalDateTime(o.CompletionStartTime),
	}
	return json.Marshal(marshaler)
}

func (o *Observation) String() string {
	if len(o.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(o.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(o); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", o)
}

type ObservationLevel string

const (
	ObservationLevelDebug   ObservationLevel = "DEBUG"
	ObservationLevelDefault ObservationLevel = "DEFAULT"
	ObservationLevelWarning ObservationLevel = "WARNING"
	ObservationLevelError   ObservationLevel = "ERROR"
)

func NewObservationLevelFromString(s string) (ObservationLevel, error) {
	switch s {
	case "DEBUG":
		return ObservationLevelDebug, nil
	case "DEFAULT":
		return ObservationLevelDefault, nil
	case "WARNING":
		return ObservationLevelWarning, nil
	case "ERROR":
		return ObservationLevelError, nil
	}
	var t ObservationLevel
	return "", fmt.Errorf("%s is not a valid %T", s, t)
}

func (o ObservationLevel) Ptr() *ObservationLevel {
	return &o
}

type Observations struct {
	Data []*Observation     `json:"data,omitempty" url:"data,omitempty"`
	Meta *UtilsMetaResponse `json:"meta,omitempty" url:"meta,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (o *Observations) GetData() []*Observation {
	if o == nil {
		return nil
	}
	return o.Data
}

func (o *Observations) GetMeta() *UtilsMetaResponse {
	if o == nil {
		return nil
	}
	return o.Meta
}

func (o *Observations) GetExtraProperties() map[string]interface{} {
	return o.extraProperties
}

func (o *Observations) UnmarshalJSON(data []byte) error {
	type unmarshaler Observations
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*o = Observations(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *o)
	if err != nil {
		return err
	}
	o.extraProperties = extraProperties
	o.rawJSON = json.RawMessage(data)
	return nil
}

func (o *Observations) String() string {
	if len(o.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(o.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(o); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", o)
}

type ObservationsView struct {
	// The unique identifier of the observation
	Id string `json:"id" url:"id"`
	// The trace ID associated with the observation
	TraceId *string `json:"traceId,omitempty" url:"traceId,omitempty"`
	// The type of the observation
	Type string `json:"type" url:"type"`
	// The name of the observation
	Name *string `json:"name,omitempty" url:"name,omitempty"`
	// The start time of the observation
	StartTime time.Time `json:"startTime" url:"startTime"`
	// The end time of the observation.
	EndTime *time.Time `json:"endTime,omitempty" url:"endTime,omitempty"`
	// The completion start time of the observation
	CompletionStartTime *time.Time `json:"completionStartTime,omitempty" url:"completionStartTime,omitempty"`
	// The model used for the observation
	Model *string `json:"model,omitempty" url:"model,omitempty"`
	// The parameters of the model used for the observation
	ModelParameters map[string]*MapValue `json:"modelParameters,omitempty" url:"modelParameters,omitempty"`
	Input           interface{}          `json:"input,omitempty" url:"input,omitempty"`
	// The version of the observation
	Version  *string     `json:"version,omitempty" url:"version,omitempty"`
	Metadata interface{} `json:"metadata,omitempty" url:"metadata,omitempty"`
	Output   interface{} `json:"output,omitempty" url:"output,omitempty"`
	// The usage data of the observation
	Usage *Usage `json:"usage,omitempty" url:"usage,omitempty"`
	// The level of the observation
	Level ObservationLevel `json:"level" url:"level"`
	// The status message of the observation
	StatusMessage *string `json:"statusMessage,omitempty" url:"statusMessage,omitempty"`
	// The parent observation ID
	ParentObservationId *string `json:"parentObservationId,omitempty" url:"parentObservationId,omitempty"`
	// The prompt ID associated with the observation
	PromptId *string `json:"promptId,omitempty" url:"promptId,omitempty"`
	// The name of the prompt associated with the observation
	PromptName *string `json:"promptName,omitempty" url:"promptName,omitempty"`
	// The version of the prompt associated with the observation
	PromptVersion *int `json:"promptVersion,omitempty" url:"promptVersion,omitempty"`
	// The unique identifier of the model
	ModelId *string `json:"modelId,omitempty" url:"modelId,omitempty"`
	// The price of the input in USD
	InputPrice *float64 `json:"inputPrice,omitempty" url:"inputPrice,omitempty"`
	// The price of the output in USD.
	OutputPrice *float64 `json:"outputPrice,omitempty" url:"outputPrice,omitempty"`
	// The total price in USD.
	TotalPrice *float64 `json:"totalPrice,omitempty" url:"totalPrice,omitempty"`
	// The calculated cost of the input in USD
	CalculatedInputCost *float64 `json:"calculatedInputCost,omitempty" url:"calculatedInputCost,omitempty"`
	// The calculated cost of the output in USD
	CalculatedOutputCost *float64 `json:"calculatedOutputCost,omitempty" url:"calculatedOutputCost,omitempty"`
	// The calculated total cost in USD
	CalculatedTotalCost *float64 `json:"calculatedTotalCost,omitempty" url:"calculatedTotalCost,omitempty"`
	// The latency in seconds.
	Latency *float64 `json:"latency,omitempty" url:"latency,omitempty"`
	// The time to the first token in seconds
	TimeToFirstToken *float64 `json:"timeToFirstToken,omitempty" url:"timeToFirstToken,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (o *ObservationsView) GetId() string {
	if o == nil {
		return ""
	}
	return o.Id
}

func (o *ObservationsView) GetTraceId() *string {
	if o == nil {
		return nil
	}
	return o.TraceId
}

func (o *ObservationsView) GetType() string {
	if o == nil {
		return ""
	}
	return o.Type
}

func (o *ObservationsView) GetName() *string {
	if o == nil {
		return nil
	}
	return o.Name
}

func (o *ObservationsView) GetStartTime() time.Time {
	if o == nil {
		return time.Time{}
	}
	return o.StartTime
}

func (o *ObservationsView) GetEndTime() *time.Time {
	if o == nil {
		return nil
	}
	return o.EndTime
}

func (o *ObservationsView) GetCompletionStartTime() *time.Time {
	if o == nil {
		return nil
	}
	return o.CompletionStartTime
}

func (o *ObservationsView) GetModel() *string {
	if o == nil {
		return nil
	}
	return o.Model
}

func (o *ObservationsView) GetModelParameters() map[string]*MapValue {
	if o == nil {
		return nil
	}
	return o.ModelParameters
}

func (o *ObservationsView) GetInput() interface{} {
	if o == nil {
		return nil
	}
	return o.Input
}

func (o *ObservationsView) GetVersion() *string {
	if o == nil {
		return nil
	}
	return o.Version
}

func (o *ObservationsView) GetMetadata() interface{} {
	if o == nil {
		return nil
	}
	return o.Metadata
}

func (o *ObservationsView) GetOutput() interface{} {
	if o == nil {
		return nil
	}
	return o.Output
}

func (o *ObservationsView) GetUsage() *Usage {
	if o == nil {
		return nil
	}
	return o.Usage
}

func (o *ObservationsView) GetLevel() ObservationLevel {
	if o == nil {
		return ""
	}
	return o.Level
}

func (o *ObservationsView) GetStatusMessage() *string {
	if o == nil {
		return nil
	}
	return o.StatusMessage
}

func (o *ObservationsView) GetParentObservationId() *string {
	if o == nil {
		return nil
	}
	return o.ParentObservationId
}

func (o *ObservationsView) GetPromptId() *string {
	if o == nil {
		return nil
	}
	return o.PromptId
}

func (o *ObservationsView) GetPromptName() *string {
	if o == nil {
		return nil
	}
	return o.PromptName
}

func (o *ObservationsView) GetPromptVersion() *int {
	if o == nil {
		return nil
	}
	return o.PromptVersion
}

func (o *ObservationsView) GetModelId() *string {
	if o == nil {
		return nil
	}
	return o.ModelId
}

func (o *ObservationsView) GetInputPrice() *float64 {
	if o == nil {
		return nil
	}
	return o.InputPrice
}

func (o *ObservationsView) GetOutputPrice() *float64 {
	if o == nil {
		return nil
	}
	return o.OutputPrice
}

func (o *ObservationsView) GetTotalPrice() *float64 {
	if o == nil {
		return nil
	}
	return o.TotalPrice
}

func (o *ObservationsView) GetCalculatedInputCost() *float64 {
	if o == nil {
		return nil
	}
	return o.CalculatedInputCost
}

func (o *ObservationsView) GetCalculatedOutputCost() *float64 {
	if o == nil {
		return nil
	}
	return o.CalculatedOutputCost
}

func (o *ObservationsView) GetCalculatedTotalCost() *float64 {
	if o == nil {
		return nil
	}
	return o.CalculatedTotalCost
}

func (o *ObservationsView) GetLatency() *float64 {
	if o == nil {
		return nil
	}
	return o.Latency
}

func (o *ObservationsView) GetTimeToFirstToken() *float64 {
	if o == nil {
		return nil
	}
	return o.TimeToFirstToken
}

func (o *ObservationsView) GetExtraProperties() map[string]interface{} {
	return o.extraProperties
}

func (o *ObservationsView) UnmarshalJSON(data []byte) error {
	type embed ObservationsView
	var unmarshaler = struct {
		embed
		StartTime           *internal.DateTime `json:"startTime"`
		EndTime             *internal.DateTime `json:"endTime,omitempty"`
		CompletionStartTime *internal.DateTime `json:"completionStartTime,omitempty"`
	}{
		embed: embed(*o),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*o = ObservationsView(unmarshaler.embed)
	o.StartTime = unmarshaler.StartTime.Time()
	o.EndTime = unmarshaler.EndTime.TimePtr()
	o.CompletionStartTime = unmarshaler.CompletionStartTime.TimePtr()
	extraProperties, err := internal.ExtractExtraProperties(data, *o)
	if err != nil {
		return err
	}
	o.extraProperties = extraProperties
	o.rawJSON = json.RawMessage(data)
	return nil
}

func (o *ObservationsView) MarshalJSON() ([]byte, error) {
	type embed ObservationsView
	var marshaler = struct {
		embed
		StartTime           *internal.DateTime `json:"startTime"`
		EndTime             *internal.DateTime `json:"endTime,omitempty"`
		CompletionStartTime *internal.DateTime `json:"completionStartTime,omitempty"`
	}{
		embed:               embed(*o),
		StartTime:           internal.NewDateTime(o.StartTime),
		EndTime:             internal.NewOptionalDateTime(o.EndTime),
		CompletionStartTime: internal.NewOptionalDateTime(o.CompletionStartTime),
	}
	return json.Marshal(marshaler)
}

func (o *ObservationsView) String() string {
	if len(o.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(o.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(o); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", o)
}

type Score struct {
	ScoreZero *ScoreZero
	ScoreOne  *ScoreOne
	ScoreTwo  *ScoreTwo

	typ string
}

func NewScoreFromScoreZero(value *ScoreZero) *Score {
	return &Score{typ: "ScoreZero", ScoreZero: value}
}

func NewScoreFromScoreOne(value *ScoreOne) *Score {
	return &Score{typ: "ScoreOne", ScoreOne: value}
}

func NewScoreFromScoreTwo(value *ScoreTwo) *Score {
	return &Score{typ: "ScoreTwo", ScoreTwo: value}
}

func (s *Score) GetScoreZero() *ScoreZero {
	if s == nil {
		return nil
	}
	return s.ScoreZero
}

func (s *Score) GetScoreOne() *ScoreOne {
	if s == nil {
		return nil
	}
	return s.ScoreOne
}

func (s *Score) GetScoreTwo() *ScoreTwo {
	if s == nil {
		return nil
	}
	return s.ScoreTwo
}

func (s *Score) UnmarshalJSON(data []byte) error {
	valueScoreZero := new(ScoreZero)
	if err := json.Unmarshal(data, &valueScoreZero); err == nil {
		s.typ = "ScoreZero"
		s.ScoreZero = valueScoreZero
		return nil
	}
	valueScoreOne := new(ScoreOne)
	if err := json.Unmarshal(data, &valueScoreOne); err == nil {
		s.typ = "ScoreOne"
		s.ScoreOne = valueScoreOne
		return nil
	}
	valueScoreTwo := new(ScoreTwo)
	if err := json.Unmarshal(data, &valueScoreTwo); err == nil {
		s.typ = "ScoreTwo"
		s.ScoreTwo = valueScoreTwo
		return nil
	}
	return fmt.Errorf("%s cannot be deserialized as a %T", data, s)
}

func (s Score) MarshalJSON() ([]byte, error) {
	if s.typ == "ScoreZero" || s.ScoreZero != nil {
		return json.Marshal(s.ScoreZero)
	}
	if s.typ == "ScoreOne" || s.ScoreOne != nil {
		return json.Marshal(s.ScoreOne)
	}
	if s.typ == "ScoreTwo" || s.ScoreTwo != nil {
		return json.Marshal(s.ScoreTwo)
	}
	return nil, fmt.Errorf("type %T does not include a non-empty union type", s)
}

type ScoreVisitor interface {
	VisitScoreZero(*ScoreZero) error
	VisitScoreOne(*ScoreOne) error
	VisitScoreTwo(*ScoreTwo) error
}

func (s *Score) Accept(visitor ScoreVisitor) error {
	if s.typ == "ScoreZero" || s.ScoreZero != nil {
		return visitor.VisitScoreZero(s.ScoreZero)
	}
	if s.typ == "ScoreOne" || s.ScoreOne != nil {
		return visitor.VisitScoreOne(s.ScoreOne)
	}
	if s.typ == "ScoreTwo" || s.ScoreTwo != nil {
		return visitor.VisitScoreTwo(s.ScoreTwo)
	}
	return fmt.Errorf("type %T does not include a non-empty union type", s)
}

type ScoreDataType string

const (
	ScoreDataTypeNumeric     ScoreDataType = "NUMERIC"
	ScoreDataTypeBoolean     ScoreDataType = "BOOLEAN"
	ScoreDataTypeCategorical ScoreDataType = "CATEGORICAL"
)

func NewScoreDataTypeFromString(s string) (ScoreDataType, error) {
	switch s {
	case "NUMERIC":
		return ScoreDataTypeNumeric, nil
	case "BOOLEAN":
		return ScoreDataTypeBoolean, nil
	case "CATEGORICAL":
		return ScoreDataTypeCategorical, nil
	}
	var t ScoreDataType
	return "", fmt.Errorf("%s is not a valid %T", s, t)
}

func (s ScoreDataType) Ptr() *ScoreDataType {
	return &s
}

type ScoreOne struct {
	Id            string      `json:"id" url:"id"`
	TraceId       string      `json:"traceId" url:"traceId"`
	Name          string      `json:"name" url:"name"`
	Source        ScoreSource `json:"source" url:"source"`
	ObservationId *string     `json:"observationId,omitempty" url:"observationId,omitempty"`
	Timestamp     time.Time   `json:"timestamp" url:"timestamp"`
	CreatedAt     time.Time   `json:"createdAt" url:"createdAt"`
	UpdatedAt     time.Time   `json:"updatedAt" url:"updatedAt"`
	AuthorUserId  *string     `json:"authorUserId,omitempty" url:"authorUserId,omitempty"`
	Comment       *string     `json:"comment,omitempty" url:"comment,omitempty"`
	// Reference a score config on a score. When set, config and score name must be equal and value must comply to optionally defined numerical range
	ConfigId *string `json:"configId,omitempty" url:"configId,omitempty"`
	// Reference an annotation queue on a score. Populated if the score was initially created in an annotation queue.
	QueueId *string `json:"queueId,omitempty" url:"queueId,omitempty"`
	// Only defined if a config is linked. Represents the numeric category mapping of the stringValue
	Value *float64 `json:"value,omitempty" url:"value,omitempty"`
	// The string representation of the score value. If no config is linked, can be any string. Otherwise, must map to a config category
	StringValue string  `json:"stringValue" url:"stringValue"`
	DataType    *string `json:"dataType,omitempty" url:"dataType,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (s *ScoreOne) GetId() string {
	if s == nil {
		return ""
	}
	return s.Id
}

func (s *ScoreOne) GetTraceId() string {
	if s == nil {
		return ""
	}
	return s.TraceId
}

func (s *ScoreOne) GetName() string {
	if s == nil {
		return ""
	}
	return s.Name
}

func (s *ScoreOne) GetSource() ScoreSource {
	if s == nil {
		return ""
	}
	return s.Source
}

func (s *ScoreOne) GetObservationId() *string {
	if s == nil {
		return nil
	}
	return s.ObservationId
}

func (s *ScoreOne) GetTimestamp() time.Time {
	if s == nil {
		return time.Time{}
	}
	return s.Timestamp
}

func (s *ScoreOne) GetCreatedAt() time.Time {
	if s == nil {
		return time.Time{}
	}
	return s.CreatedAt
}

func (s *ScoreOne) GetUpdatedAt() time.Time {
	if s == nil {
		return time.Time{}
	}
	return s.UpdatedAt
}

func (s *ScoreOne) GetAuthorUserId() *string {
	if s == nil {
		return nil
	}
	return s.AuthorUserId
}

func (s *ScoreOne) GetComment() *string {
	if s == nil {
		return nil
	}
	return s.Comment
}

func (s *ScoreOne) GetConfigId() *string {
	if s == nil {
		return nil
	}
	return s.ConfigId
}

func (s *ScoreOne) GetQueueId() *string {
	if s == nil {
		return nil
	}
	return s.QueueId
}

func (s *ScoreOne) GetValue() *float64 {
	if s == nil {
		return nil
	}
	return s.Value
}

func (s *ScoreOne) GetStringValue() string {
	if s == nil {
		return ""
	}
	return s.StringValue
}

func (s *ScoreOne) GetExtraProperties() map[string]interface{} {
	return s.extraProperties
}

func (s *ScoreOne) UnmarshalJSON(data []byte) error {
	type embed ScoreOne
	var unmarshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed: embed(*s),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*s = ScoreOne(unmarshaler.embed)
	s.Timestamp = unmarshaler.Timestamp.Time()
	s.CreatedAt = unmarshaler.CreatedAt.Time()
	s.UpdatedAt = unmarshaler.UpdatedAt.Time()
	extraProperties, err := internal.ExtractExtraProperties(data, *s)
	if err != nil {
		return err
	}
	s.extraProperties = extraProperties
	s.rawJSON = json.RawMessage(data)
	return nil
}

func (s *ScoreOne) MarshalJSON() ([]byte, error) {
	type embed ScoreOne
	var marshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed:     embed(*s),
		Timestamp: internal.NewDateTime(s.Timestamp),
		CreatedAt: internal.NewDateTime(s.CreatedAt),
		UpdatedAt: internal.NewDateTime(s.UpdatedAt),
	}
	return json.Marshal(marshaler)
}

func (s *ScoreOne) String() string {
	if len(s.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(s.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(s); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", s)
}

type ScoreSource string

const (
	ScoreSourceAnnotation ScoreSource = "ANNOTATION"
	ScoreSourceApi        ScoreSource = "API"
	ScoreSourceEval       ScoreSource = "EVAL"
)

func NewScoreSourceFromString(s string) (ScoreSource, error) {
	switch s {
	case "ANNOTATION":
		return ScoreSourceAnnotation, nil
	case "API":
		return ScoreSourceApi, nil
	case "EVAL":
		return ScoreSourceEval, nil
	}
	var t ScoreSource
	return "", fmt.Errorf("%s is not a valid %T", s, t)
}

func (s ScoreSource) Ptr() *ScoreSource {
	return &s
}

type ScoreTwo struct {
	Id            string      `json:"id" url:"id"`
	TraceId       string      `json:"traceId" url:"traceId"`
	Name          string      `json:"name" url:"name"`
	Source        ScoreSource `json:"source" url:"source"`
	ObservationId *string     `json:"observationId,omitempty" url:"observationId,omitempty"`
	Timestamp     time.Time   `json:"timestamp" url:"timestamp"`
	CreatedAt     time.Time   `json:"createdAt" url:"createdAt"`
	UpdatedAt     time.Time   `json:"updatedAt" url:"updatedAt"`
	AuthorUserId  *string     `json:"authorUserId,omitempty" url:"authorUserId,omitempty"`
	Comment       *string     `json:"comment,omitempty" url:"comment,omitempty"`
	// Reference a score config on a score. When set, config and score name must be equal and value must comply to optionally defined numerical range
	ConfigId *string `json:"configId,omitempty" url:"configId,omitempty"`
	// Reference an annotation queue on a score. Populated if the score was initially created in an annotation queue.
	QueueId *string `json:"queueId,omitempty" url:"queueId,omitempty"`
	// The numeric value of the score. Equals 1 for "True" and 0 for "False"
	Value float64 `json:"value" url:"value"`
	// The string representation of the score value. Is inferred from the numeric value and equals "True" or "False"
	StringValue string  `json:"stringValue" url:"stringValue"`
	DataType    *string `json:"dataType,omitempty" url:"dataType,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (s *ScoreTwo) GetId() string {
	if s == nil {
		return ""
	}
	return s.Id
}

func (s *ScoreTwo) GetTraceId() string {
	if s == nil {
		return ""
	}
	return s.TraceId
}

func (s *ScoreTwo) GetName() string {
	if s == nil {
		return ""
	}
	return s.Name
}

func (s *ScoreTwo) GetSource() ScoreSource {
	if s == nil {
		return ""
	}
	return s.Source
}

func (s *ScoreTwo) GetObservationId() *string {
	if s == nil {
		return nil
	}
	return s.ObservationId
}

func (s *ScoreTwo) GetTimestamp() time.Time {
	if s == nil {
		return time.Time{}
	}
	return s.Timestamp
}

func (s *ScoreTwo) GetCreatedAt() time.Time {
	if s == nil {
		return time.Time{}
	}
	return s.CreatedAt
}

func (s *ScoreTwo) GetUpdatedAt() time.Time {
	if s == nil {
		return time.Time{}
	}
	return s.UpdatedAt
}

func (s *ScoreTwo) GetAuthorUserId() *string {
	if s == nil {
		return nil
	}
	return s.AuthorUserId
}

func (s *ScoreTwo) GetComment() *string {
	if s == nil {
		return nil
	}
	return s.Comment
}

func (s *ScoreTwo) GetConfigId() *string {
	if s == nil {
		return nil
	}
	return s.ConfigId
}

func (s *ScoreTwo) GetQueueId() *string {
	if s == nil {
		return nil
	}
	return s.QueueId
}

func (s *ScoreTwo) GetValue() float64 {
	if s == nil {
		return 0
	}
	return s.Value
}

func (s *ScoreTwo) GetStringValue() string {
	if s == nil {
		return ""
	}
	return s.StringValue
}

func (s *ScoreTwo) GetExtraProperties() map[string]interface{} {
	return s.extraProperties
}

func (s *ScoreTwo) UnmarshalJSON(data []byte) error {
	type embed ScoreTwo
	var unmarshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed: embed(*s),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*s = ScoreTwo(unmarshaler.embed)
	s.Timestamp = unmarshaler.Timestamp.Time()
	s.CreatedAt = unmarshaler.CreatedAt.Time()
	s.UpdatedAt = unmarshaler.UpdatedAt.Time()
	extraProperties, err := internal.ExtractExtraProperties(data, *s)
	if err != nil {
		return err
	}
	s.extraProperties = extraProperties
	s.rawJSON = json.RawMessage(data)
	return nil
}

func (s *ScoreTwo) MarshalJSON() ([]byte, error) {
	type embed ScoreTwo
	var marshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed:     embed(*s),
		Timestamp: internal.NewDateTime(s.Timestamp),
		CreatedAt: internal.NewDateTime(s.CreatedAt),
		UpdatedAt: internal.NewDateTime(s.UpdatedAt),
	}
	return json.Marshal(marshaler)
}

func (s *ScoreTwo) String() string {
	if len(s.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(s.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(s); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", s)
}

type ScoreZero struct {
	Id            string      `json:"id" url:"id"`
	TraceId       string      `json:"traceId" url:"traceId"`
	Name          string      `json:"name" url:"name"`
	Source        ScoreSource `json:"source" url:"source"`
	ObservationId *string     `json:"observationId,omitempty" url:"observationId,omitempty"`
	Timestamp     time.Time   `json:"timestamp" url:"timestamp"`
	CreatedAt     time.Time   `json:"createdAt" url:"createdAt"`
	UpdatedAt     time.Time   `json:"updatedAt" url:"updatedAt"`
	AuthorUserId  *string     `json:"authorUserId,omitempty" url:"authorUserId,omitempty"`
	Comment       *string     `json:"comment,omitempty" url:"comment,omitempty"`
	// Reference a score config on a score. When set, config and score name must be equal and value must comply to optionally defined numerical range
	ConfigId *string `json:"configId,omitempty" url:"configId,omitempty"`
	// Reference an annotation queue on a score. Populated if the score was initially created in an annotation queue.
	QueueId *string `json:"queueId,omitempty" url:"queueId,omitempty"`
	// The numeric value of the score
	Value    float64 `json:"value" url:"value"`
	DataType *string `json:"dataType,omitempty" url:"dataType,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (s *ScoreZero) GetId() string {
	if s == nil {
		return ""
	}
	return s.Id
}

func (s *ScoreZero) GetTraceId() string {
	if s == nil {
		return ""
	}
	return s.TraceId
}

func (s *ScoreZero) GetName() string {
	if s == nil {
		return ""
	}
	return s.Name
}

func (s *ScoreZero) GetSource() ScoreSource {
	if s == nil {
		return ""
	}
	return s.Source
}

func (s *ScoreZero) GetObservationId() *string {
	if s == nil {
		return nil
	}
	return s.ObservationId
}

func (s *ScoreZero) GetTimestamp() time.Time {
	if s == nil {
		return time.Time{}
	}
	return s.Timestamp
}

func (s *ScoreZero) GetCreatedAt() time.Time {
	if s == nil {
		return time.Time{}
	}
	return s.CreatedAt
}

func (s *ScoreZero) GetUpdatedAt() time.Time {
	if s == nil {
		return time.Time{}
	}
	return s.UpdatedAt
}

func (s *ScoreZero) GetAuthorUserId() *string {
	if s == nil {
		return nil
	}
	return s.AuthorUserId
}

func (s *ScoreZero) GetComment() *string {
	if s == nil {
		return nil
	}
	return s.Comment
}

func (s *ScoreZero) GetConfigId() *string {
	if s == nil {
		return nil
	}
	return s.ConfigId
}

func (s *ScoreZero) GetQueueId() *string {
	if s == nil {
		return nil
	}
	return s.QueueId
}

func (s *ScoreZero) GetValue() float64 {
	if s == nil {
		return 0
	}
	return s.Value
}

func (s *ScoreZero) GetExtraProperties() map[string]interface{} {
	return s.extraProperties
}

func (s *ScoreZero) UnmarshalJSON(data []byte) error {
	type embed ScoreZero
	var unmarshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed: embed(*s),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*s = ScoreZero(unmarshaler.embed)
	s.Timestamp = unmarshaler.Timestamp.Time()
	s.CreatedAt = unmarshaler.CreatedAt.Time()
	s.UpdatedAt = unmarshaler.UpdatedAt.Time()
	extraProperties, err := internal.ExtractExtraProperties(data, *s)
	if err != nil {
		return err
	}
	s.extraProperties = extraProperties
	s.rawJSON = json.RawMessage(data)
	return nil
}

func (s *ScoreZero) MarshalJSON() ([]byte, error) {
	type embed ScoreZero
	var marshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed:     embed(*s),
		Timestamp: internal.NewDateTime(s.Timestamp),
		CreatedAt: internal.NewDateTime(s.CreatedAt),
		UpdatedAt: internal.NewDateTime(s.UpdatedAt),
	}
	return json.Marshal(marshaler)
}

func (s *ScoreZero) String() string {
	if len(s.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(s.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(s); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", s)
}

type Sort struct {
	Id string `json:"id" url:"id"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (s *Sort) GetId() string {
	if s == nil {
		return ""
	}
	return s.Id
}

func (s *Sort) GetExtraProperties() map[string]interface{} {
	return s.extraProperties
}

func (s *Sort) UnmarshalJSON(data []byte) error {
	type unmarshaler Sort
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*s = Sort(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *s)
	if err != nil {
		return err
	}
	s.extraProperties = extraProperties
	s.rawJSON = json.RawMessage(data)
	return nil
}

func (s *Sort) String() string {
	if len(s.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(s.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(s); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", s)
}

type Trace struct {
	// The unique identifier of a trace
	Id string `json:"id" url:"id"`
	// The timestamp when the trace was created
	Timestamp time.Time `json:"timestamp" url:"timestamp"`
	// The name of the trace
	Name   *string     `json:"name,omitempty" url:"name,omitempty"`
	Input  interface{} `json:"input,omitempty" url:"input,omitempty"`
	Output interface{} `json:"output,omitempty" url:"output,omitempty"`
	// The session identifier associated with the trace
	SessionId *string `json:"sessionId,omitempty" url:"sessionId,omitempty"`
	// The release version of the application when the trace was created
	Release *string `json:"release,omitempty" url:"release,omitempty"`
	// The version of the trace
	Version *string `json:"version,omitempty" url:"version,omitempty"`
	// The user identifier associated with the trace
	UserId   *string     `json:"userId,omitempty" url:"userId,omitempty"`
	Metadata interface{} `json:"metadata,omitempty" url:"metadata,omitempty"`
	// The tags associated with the trace. Can be an array of strings or null.
	Tags []string `json:"tags,omitempty" url:"tags,omitempty"`
	// Public traces are accessible via url without login
	Public *bool `json:"public,omitempty" url:"public,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (t *Trace) GetId() string {
	if t == nil {
		return ""
	}
	return t.Id
}

func (t *Trace) GetTimestamp() time.Time {
	if t == nil {
		return time.Time{}
	}
	return t.Timestamp
}

func (t *Trace) GetName() *string {
	if t == nil {
		return nil
	}
	return t.Name
}

func (t *Trace) GetInput() interface{} {
	if t == nil {
		return nil
	}
	return t.Input
}

func (t *Trace) GetOutput() interface{} {
	if t == nil {
		return nil
	}
	return t.Output
}

func (t *Trace) GetSessionId() *string {
	if t == nil {
		return nil
	}
	return t.SessionId
}

func (t *Trace) GetRelease() *string {
	if t == nil {
		return nil
	}
	return t.Release
}

func (t *Trace) GetVersion() *string {
	if t == nil {
		return nil
	}
	return t.Version
}

func (t *Trace) GetUserId() *string {
	if t == nil {
		return nil
	}
	return t.UserId
}

func (t *Trace) GetMetadata() interface{} {
	if t == nil {
		return nil
	}
	return t.Metadata
}

func (t *Trace) GetTags() []string {
	if t == nil {
		return nil
	}
	return t.Tags
}

func (t *Trace) GetPublic() *bool {
	if t == nil {
		return nil
	}
	return t.Public
}

func (t *Trace) GetExtraProperties() map[string]interface{} {
	return t.extraProperties
}

func (t *Trace) UnmarshalJSON(data []byte) error {
	type embed Trace
	var unmarshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
	}{
		embed: embed(*t),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*t = Trace(unmarshaler.embed)
	t.Timestamp = unmarshaler.Timestamp.Time()
	extraProperties, err := internal.ExtractExtraProperties(data, *t)
	if err != nil {
		return err
	}
	t.extraProperties = extraProperties
	t.rawJSON = json.RawMessage(data)
	return nil
}

func (t *Trace) MarshalJSON() ([]byte, error) {
	type embed Trace
	var marshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
	}{
		embed:     embed(*t),
		Timestamp: internal.NewDateTime(t.Timestamp),
	}
	return json.Marshal(marshaler)
}

func (t *Trace) String() string {
	if len(t.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(t.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(t); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", t)
}

// Standard interface for usage and cost
type Usage struct {
	// Number of input units (e.g. tokens)
	Input *int `json:"input,omitempty" url:"input,omitempty"`
	// Number of output units (e.g. tokens)
	Output *int `json:"output,omitempty" url:"output,omitempty"`
	// Defaults to input+output if not set
	Total *int            `json:"total,omitempty" url:"total,omitempty"`
	Unit  *ModelUsageUnit `json:"unit,omitempty" url:"unit,omitempty"`
	// USD input cost
	InputCost *float64 `json:"inputCost,omitempty" url:"inputCost,omitempty"`
	// USD output cost
	OutputCost *float64 `json:"outputCost,omitempty" url:"outputCost,omitempty"`
	// USD total cost, defaults to input+output
	TotalCost *float64 `json:"totalCost,omitempty" url:"totalCost,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (u *Usage) GetInput() *int {
	if u == nil {
		return nil
	}
	return u.Input
}

func (u *Usage) GetOutput() *int {
	if u == nil {
		return nil
	}
	return u.Output
}

func (u *Usage) GetTotal() *int {
	if u == nil {
		return nil
	}
	return u.Total
}

func (u *Usage) GetUnit() *ModelUsageUnit {
	if u == nil {
		return nil
	}
	return u.Unit
}

func (u *Usage) GetInputCost() *float64 {
	if u == nil {
		return nil
	}
	return u.InputCost
}

func (u *Usage) GetOutputCost() *float64 {
	if u == nil {
		return nil
	}
	return u.OutputCost
}

func (u *Usage) GetTotalCost() *float64 {
	if u == nil {
		return nil
	}
	return u.TotalCost
}

func (u *Usage) GetExtraProperties() map[string]interface{} {
	return u.extraProperties
}

func (u *Usage) UnmarshalJSON(data []byte) error {
	type unmarshaler Usage
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*u = Usage(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *u)
	if err != nil {
		return err
	}
	u.extraProperties = extraProperties
	u.rawJSON = json.RawMessage(data)
	return nil
}

func (u *Usage) String() string {
	if len(u.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(u.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(u); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", u)
}

type UtilsMetaResponse struct {
	// current page number
	Page int `json:"page" url:"page"`
	// number of items per page
	Limit int `json:"limit" url:"limit"`
	// number of total items given the current filters/selection (if any)
	TotalItems int `json:"totalItems" url:"totalItems"`
	// number of total pages given the current limit
	TotalPages int `json:"totalPages" url:"totalPages"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (u *UtilsMetaResponse) GetPage() int {
	if u == nil {
		return 0
	}
	return u.Page
}

func (u *UtilsMetaResponse) GetLimit() int {
	if u == nil {
		return 0
	}
	return u.Limit
}

func (u *UtilsMetaResponse) GetTotalItems() int {
	if u == nil {
		return 0
	}
	return u.TotalItems
}

func (u *UtilsMetaResponse) GetTotalPages() int {
	if u == nil {
		return 0
	}
	return u.TotalPages
}

func (u *UtilsMetaResponse) GetExtraProperties() map[string]interface{} {
	return u.extraProperties
}

func (u *UtilsMetaResponse) UnmarshalJSON(data []byte) error {
	type unmarshaler UtilsMetaResponse
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*u = UtilsMetaResponse(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *u)
	if err != nil {
		return err
	}
	u.extraProperties = extraProperties
	u.rawJSON = json.RawMessage(data)
	return nil
}

func (u *UtilsMetaResponse) String() string {
	if len(u.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(u.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(u); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", u)
}
