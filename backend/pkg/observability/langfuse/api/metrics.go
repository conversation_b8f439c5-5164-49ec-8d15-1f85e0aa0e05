// This file was auto-generated by <PERSON>rn from our API Definition.

package api

import (
	json "encoding/json"
	fmt "fmt"
	internal "pentagi/pkg/observability/langfuse/api/internal"
	time "time"
)

type MetricsDailyRequest struct {
	// page number, starts at 1
	Page *int `json:"-" url:"page,omitempty"`
	// limit of items per page
	Limit *int `json:"-" url:"limit,omitempty"`
	// Optional filter by the name of the trace
	TraceName *string `json:"-" url:"traceName,omitempty"`
	// Optional filter by the userId associated with the trace
	UserId *string `json:"-" url:"userId,omitempty"`
	// Optional filter for metrics where traces include all of these tags
	Tags []*string `json:"-" url:"tags,omitempty"`
	// Optional filter to only include traces and observations on or after a certain datetime (ISO 8601)
	FromTimestamp *time.Time `json:"-" url:"fromTimestamp,omitempty"`
	// Optional filter to only include traces and observations before a certain datetime (ISO 8601)
	ToTimestamp *time.Time `json:"-" url:"toTimestamp,omitempty"`
}

type DailyMetrics struct {
	// A list of daily metrics, only days with ingested data are included.
	Data []*DailyMetricsDetails `json:"data,omitempty" url:"data,omitempty"`
	Meta *UtilsMetaResponse     `json:"meta,omitempty" url:"meta,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (d *DailyMetrics) GetData() []*DailyMetricsDetails {
	if d == nil {
		return nil
	}
	return d.Data
}

func (d *DailyMetrics) GetMeta() *UtilsMetaResponse {
	if d == nil {
		return nil
	}
	return d.Meta
}

func (d *DailyMetrics) GetExtraProperties() map[string]interface{} {
	return d.extraProperties
}

func (d *DailyMetrics) UnmarshalJSON(data []byte) error {
	type unmarshaler DailyMetrics
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*d = DailyMetrics(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *d)
	if err != nil {
		return err
	}
	d.extraProperties = extraProperties
	d.rawJSON = json.RawMessage(data)
	return nil
}

func (d *DailyMetrics) String() string {
	if len(d.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(d.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(d); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", d)
}

type DailyMetricsDetails struct {
	Date              string `json:"date" url:"date"`
	CountTraces       int    `json:"countTraces" url:"countTraces"`
	CountObservations int    `json:"countObservations" url:"countObservations"`
	// Total model cost in USD
	TotalCost float64         `json:"totalCost" url:"totalCost"`
	Usage     []*UsageByModel `json:"usage,omitempty" url:"usage,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (d *DailyMetricsDetails) GetDate() string {
	if d == nil {
		return ""
	}
	return d.Date
}

func (d *DailyMetricsDetails) GetCountTraces() int {
	if d == nil {
		return 0
	}
	return d.CountTraces
}

func (d *DailyMetricsDetails) GetCountObservations() int {
	if d == nil {
		return 0
	}
	return d.CountObservations
}

func (d *DailyMetricsDetails) GetTotalCost() float64 {
	if d == nil {
		return 0
	}
	return d.TotalCost
}

func (d *DailyMetricsDetails) GetUsage() []*UsageByModel {
	if d == nil {
		return nil
	}
	return d.Usage
}

func (d *DailyMetricsDetails) GetExtraProperties() map[string]interface{} {
	return d.extraProperties
}

func (d *DailyMetricsDetails) UnmarshalJSON(data []byte) error {
	type unmarshaler DailyMetricsDetails
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*d = DailyMetricsDetails(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *d)
	if err != nil {
		return err
	}
	d.extraProperties = extraProperties
	d.rawJSON = json.RawMessage(data)
	return nil
}

func (d *DailyMetricsDetails) String() string {
	if len(d.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(d.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(d); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", d)
}

// Daily usage of a given model. Usage corresponds to the unit set for the specific model (e.g. tokens).
type UsageByModel struct {
	Model *string `json:"model,omitempty" url:"model,omitempty"`
	// Total number of generation input units (e.g. tokens)
	InputUsage int `json:"inputUsage" url:"inputUsage"`
	// Total number of generation output units (e.g. tokens)
	OutputUsage int `json:"outputUsage" url:"outputUsage"`
	// Total number of generation total units (e.g. tokens)
	TotalUsage        int `json:"totalUsage" url:"totalUsage"`
	CountTraces       int `json:"countTraces" url:"countTraces"`
	CountObservations int `json:"countObservations" url:"countObservations"`
	// Total model cost in USD
	TotalCost float64 `json:"totalCost" url:"totalCost"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (u *UsageByModel) GetModel() *string {
	if u == nil {
		return nil
	}
	return u.Model
}

func (u *UsageByModel) GetInputUsage() int {
	if u == nil {
		return 0
	}
	return u.InputUsage
}

func (u *UsageByModel) GetOutputUsage() int {
	if u == nil {
		return 0
	}
	return u.OutputUsage
}

func (u *UsageByModel) GetTotalUsage() int {
	if u == nil {
		return 0
	}
	return u.TotalUsage
}

func (u *UsageByModel) GetCountTraces() int {
	if u == nil {
		return 0
	}
	return u.CountTraces
}

func (u *UsageByModel) GetCountObservations() int {
	if u == nil {
		return 0
	}
	return u.CountObservations
}

func (u *UsageByModel) GetTotalCost() float64 {
	if u == nil {
		return 0
	}
	return u.TotalCost
}

func (u *UsageByModel) GetExtraProperties() map[string]interface{} {
	return u.extraProperties
}

func (u *UsageByModel) UnmarshalJSON(data []byte) error {
	type unmarshaler UsageByModel
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*u = UsageByModel(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *u)
	if err != nil {
		return err
	}
	u.extraProperties = extraProperties
	u.rawJSON = json.RawMessage(data)
	return nil
}

func (u *UsageByModel) String() string {
	if len(u.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(u.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(u); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", u)
}
