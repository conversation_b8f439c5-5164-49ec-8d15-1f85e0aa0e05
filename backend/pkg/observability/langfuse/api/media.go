// This file was auto-generated by <PERSON><PERSON> from our API Definition.

package api

import (
	json "encoding/json"
	fmt "fmt"
	internal "pentagi/pkg/observability/langfuse/api/internal"
	time "time"
)

type GetMediaUploadUrlRequest struct {
	// The trace ID associated with the media record
	TraceId string `json:"traceId" url:"-"`
	// The observation ID associated with the media record. If the media record is associated directly with a trace, this will be null.
	ObservationId *string          `json:"observationId,omitempty" url:"-"`
	ContentType   MediaContentType `json:"contentType,omitempty" url:"-"`
	// The size of the media record in bytes
	ContentLength int `json:"contentLength" url:"-"`
	// The SHA-256 hash of the media record
	Sha256Hash string `json:"sha256Hash" url:"-"`
	// The trace / observation field the media record is associated with. This can be one of `input`, `output`, `metadata`
	Field string `json:"field" url:"-"`
}

type PatchMediaBody struct {
	// The date and time when the media record was uploaded
	UploadedAt time.Time `json:"uploadedAt" url:"-"`
	// The HTTP status code of the upload
	UploadHttpStatus int `json:"uploadHttpStatus" url:"-"`
	// The HTTP error message of the upload
	UploadHttpError *string `json:"uploadHttpError,omitempty" url:"-"`
	// The time in milliseconds it took to upload the media record
	UploadTimeMs *int `json:"uploadTimeMs,omitempty" url:"-"`
}

func (p *PatchMediaBody) UnmarshalJSON(data []byte) error {
	type unmarshaler PatchMediaBody
	var body unmarshaler
	if err := json.Unmarshal(data, &body); err != nil {
		return err
	}
	*p = PatchMediaBody(body)
	return nil
}

func (p *PatchMediaBody) MarshalJSON() ([]byte, error) {
	type embed PatchMediaBody
	var marshaler = struct {
		embed
		UploadedAt *internal.DateTime `json:"uploadedAt"`
	}{
		embed:      embed(*p),
		UploadedAt: internal.NewDateTime(p.UploadedAt),
	}
	return json.Marshal(marshaler)
}

type GetMediaResponse struct {
	// The unique langfuse identifier of a media record
	MediaId string `json:"mediaId" url:"mediaId"`
	// The MIME type of the media record
	ContentType string `json:"contentType" url:"contentType"`
	// The size of the media record in bytes
	ContentLength int `json:"contentLength" url:"contentLength"`
	// The date and time when the media record was uploaded
	UploadedAt time.Time `json:"uploadedAt" url:"uploadedAt"`
	// The download URL of the media record
	Url string `json:"url" url:"url"`
	// The expiry date and time of the media record download URL
	UrlExpiry string `json:"urlExpiry" url:"urlExpiry"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (g *GetMediaResponse) GetMediaId() string {
	if g == nil {
		return ""
	}
	return g.MediaId
}

func (g *GetMediaResponse) GetContentType() string {
	if g == nil {
		return ""
	}
	return g.ContentType
}

func (g *GetMediaResponse) GetContentLength() int {
	if g == nil {
		return 0
	}
	return g.ContentLength
}

func (g *GetMediaResponse) GetUploadedAt() time.Time {
	if g == nil {
		return time.Time{}
	}
	return g.UploadedAt
}

func (g *GetMediaResponse) GetUrl() string {
	if g == nil {
		return ""
	}
	return g.Url
}

func (g *GetMediaResponse) GetUrlExpiry() string {
	if g == nil {
		return ""
	}
	return g.UrlExpiry
}

func (g *GetMediaResponse) GetExtraProperties() map[string]interface{} {
	return g.extraProperties
}

func (g *GetMediaResponse) UnmarshalJSON(data []byte) error {
	type embed GetMediaResponse
	var unmarshaler = struct {
		embed
		UploadedAt *internal.DateTime `json:"uploadedAt"`
	}{
		embed: embed(*g),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*g = GetMediaResponse(unmarshaler.embed)
	g.UploadedAt = unmarshaler.UploadedAt.Time()
	extraProperties, err := internal.ExtractExtraProperties(data, *g)
	if err != nil {
		return err
	}
	g.extraProperties = extraProperties
	g.rawJSON = json.RawMessage(data)
	return nil
}

func (g *GetMediaResponse) MarshalJSON() ([]byte, error) {
	type embed GetMediaResponse
	var marshaler = struct {
		embed
		UploadedAt *internal.DateTime `json:"uploadedAt"`
	}{
		embed:      embed(*g),
		UploadedAt: internal.NewDateTime(g.UploadedAt),
	}
	return json.Marshal(marshaler)
}

func (g *GetMediaResponse) String() string {
	if len(g.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(g.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(g); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", g)
}

type GetMediaUploadUrlResponse struct {
	// The presigned upload URL. If the asset is already uploaded, this will be null
	UploadUrl *string `json:"uploadUrl,omitempty" url:"uploadUrl,omitempty"`
	// The unique langfuse identifier of a media record
	MediaId string `json:"mediaId" url:"mediaId"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (g *GetMediaUploadUrlResponse) GetUploadUrl() *string {
	if g == nil {
		return nil
	}
	return g.UploadUrl
}

func (g *GetMediaUploadUrlResponse) GetMediaId() string {
	if g == nil {
		return ""
	}
	return g.MediaId
}

func (g *GetMediaUploadUrlResponse) GetExtraProperties() map[string]interface{} {
	return g.extraProperties
}

func (g *GetMediaUploadUrlResponse) UnmarshalJSON(data []byte) error {
	type unmarshaler GetMediaUploadUrlResponse
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*g = GetMediaUploadUrlResponse(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *g)
	if err != nil {
		return err
	}
	g.extraProperties = extraProperties
	g.rawJSON = json.RawMessage(data)
	return nil
}

func (g *GetMediaUploadUrlResponse) String() string {
	if len(g.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(g.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(g); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", g)
}

// The MIME type of the media record
type MediaContentType = string
