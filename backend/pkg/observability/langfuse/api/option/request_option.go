// This file was auto-generated by Fern from our API Definition.

package option

import (
	http "net/http"
	url "net/url"
	core "pentagi/pkg/observability/langfuse/api/core"
)

// RequestOption adapts the behavior of an indivdual request.
type RequestOption = core.RequestOption

// WithBaseURL sets the base URL, overriding the default
// environment, if any.
func WithBaseURL(baseURL string) *core.BaseURLOption {
	return &core.BaseURLOption{
		BaseURL: baseURL,
	}
}

// WithHTTPClient uses the given HTTPClient to issue the request.
func WithHTTPClient(httpClient core.HTTPClient) *core.HTTPClientOption {
	return &core.HTTPClientOption{
		HTTPClient: httpClient,
	}
}

// WithHTTPHeader adds the given http.Header to the request.
func WithHTTPHeader(httpHeader http.Header) *core.HTTPHeaderOption {
	return &core.HTTPHeaderOption{
		// Clone the headers so they can't be modified after the option call.
		HTTPHeader: httpHeader.Clone(),
	}
}

// WithBodyProperties adds the given body properties to the request.
func WithBodyProperties(bodyProperties map[string]interface{}) *core.BodyPropertiesOption {
	copiedBodyProperties := make(map[string]interface{}, len(bodyProperties))
	for key, value := range bodyProperties {
		copiedBodyProperties[key] = value
	}
	return &core.BodyPropertiesOption{
		BodyProperties: copiedBodyProperties,
	}
}

// WithQueryParameters adds the given query parameters to the request.
func WithQueryParameters(queryParameters url.Values) *core.QueryParametersOption {
	copiedQueryParameters := make(url.Values, len(queryParameters))
	for key, values := range queryParameters {
		copiedQueryParameters[key] = values
	}
	return &core.QueryParametersOption{
		QueryParameters: copiedQueryParameters,
	}
}

// WithMaxAttempts configures the maximum number of retry attempts.
func WithMaxAttempts(attempts uint) *core.MaxAttemptsOption {
	return &core.MaxAttemptsOption{
		MaxAttempts: attempts,
	}
}

// WithBasicAuth sets the 'Authorization: Basic <base64>' request header.
func WithBasicAuth(username, password string) *core.BasicAuthOption {
	return &core.BasicAuthOption{
		Username: username,
		Password: password,
	}
}
