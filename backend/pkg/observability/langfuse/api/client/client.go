// This file was auto-generated by Fern from our API Definition.

package client

import (
	http "net/http"
	comments "pentagi/pkg/observability/langfuse/api/comments"
	core "pentagi/pkg/observability/langfuse/api/core"
	datasetitems "pentagi/pkg/observability/langfuse/api/datasetitems"
	datasetrunitems "pentagi/pkg/observability/langfuse/api/datasetrunitems"
	datasets "pentagi/pkg/observability/langfuse/api/datasets"
	health "pentagi/pkg/observability/langfuse/api/health"
	ingestion "pentagi/pkg/observability/langfuse/api/ingestion"
	internal "pentagi/pkg/observability/langfuse/api/internal"
	media "pentagi/pkg/observability/langfuse/api/media"
	metrics "pentagi/pkg/observability/langfuse/api/metrics"
	models "pentagi/pkg/observability/langfuse/api/models"
	observations "pentagi/pkg/observability/langfuse/api/observations"
	option "pentagi/pkg/observability/langfuse/api/option"
	projects "pentagi/pkg/observability/langfuse/api/projects"
	prompts "pentagi/pkg/observability/langfuse/api/prompts"
	score "pentagi/pkg/observability/langfuse/api/score"
	scoreconfigs "pentagi/pkg/observability/langfuse/api/scoreconfigs"
	sessions "pentagi/pkg/observability/langfuse/api/sessions"
	trace "pentagi/pkg/observability/langfuse/api/trace"
)

type Client struct {
	baseURL string
	caller  *internal.Caller
	header  http.Header

	Comments        *comments.Client
	Datasetitems    *datasetitems.Client
	Datasetrunitems *datasetrunitems.Client
	Datasets        *datasets.Client
	Health          *health.Client
	Ingestion       *ingestion.Client
	Media           *media.Client
	Metrics         *metrics.Client
	Models          *models.Client
	Observations    *observations.Client
	Projects        *projects.Client
	Prompts         *prompts.Client
	Scoreconfigs    *scoreconfigs.Client
	Score           *score.Client
	Sessions        *sessions.Client
	Trace           *trace.Client
}

func NewClient(opts ...option.RequestOption) *Client {
	options := core.NewRequestOptions(opts...)
	return &Client{
		baseURL: options.BaseURL,
		caller: internal.NewCaller(
			&internal.CallerParams{
				Client:      options.HTTPClient,
				MaxAttempts: options.MaxAttempts,
			},
		),
		header:          options.ToHeader(),
		Comments:        comments.NewClient(opts...),
		Datasetitems:    datasetitems.NewClient(opts...),
		Datasetrunitems: datasetrunitems.NewClient(opts...),
		Datasets:        datasets.NewClient(opts...),
		Health:          health.NewClient(opts...),
		Ingestion:       ingestion.NewClient(opts...),
		Media:           media.NewClient(opts...),
		Metrics:         metrics.NewClient(opts...),
		Models:          models.NewClient(opts...),
		Observations:    observations.NewClient(opts...),
		Projects:        projects.NewClient(opts...),
		Prompts:         prompts.NewClient(opts...),
		Scoreconfigs:    scoreconfigs.NewClient(opts...),
		Score:           score.NewClient(opts...),
		Sessions:        sessions.NewClient(opts...),
		Trace:           trace.NewClient(opts...),
	}
}
