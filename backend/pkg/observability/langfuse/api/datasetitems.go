// This file was auto-generated by <PERSON><PERSON> from our API Definition.

package api

import (
	json "encoding/json"
	fmt "fmt"
	internal "pentagi/pkg/observability/langfuse/api/internal"
	time "time"
)

type CreateDatasetItemRequest struct {
	DatasetName         string      `json:"datasetName" url:"-"`
	Input               interface{} `json:"input,omitempty" url:"-"`
	ExpectedOutput      interface{} `json:"expectedOutput,omitempty" url:"-"`
	Metadata            interface{} `json:"metadata,omitempty" url:"-"`
	SourceTraceId       *string     `json:"sourceTraceId,omitempty" url:"-"`
	SourceObservationId *string     `json:"sourceObservationId,omitempty" url:"-"`
	// Dataset items are upserted on their id. Id needs to be unique (project-level) and cannot be reused across datasets.
	Id *string `json:"id,omitempty" url:"-"`
	// Defaults to ACTIVE for newly created items
	Status *DatasetStatus `json:"status,omitempty" url:"-"`
}

type DatasetItemsListRequest struct {
	DatasetName         *string `json:"-" url:"datasetName,omitempty"`
	SourceTraceId       *string `json:"-" url:"sourceTraceId,omitempty"`
	SourceObservationId *string `json:"-" url:"sourceObservationId,omitempty"`
	// page number, starts at 1
	Page *int `json:"-" url:"page,omitempty"`
	// limit of items per page
	Limit *int `json:"-" url:"limit,omitempty"`
}

type DatasetItem struct {
	Id                  string        `json:"id" url:"id"`
	Status              DatasetStatus `json:"status" url:"status"`
	Input               interface{}   `json:"input,omitempty" url:"input,omitempty"`
	ExpectedOutput      interface{}   `json:"expectedOutput,omitempty" url:"expectedOutput,omitempty"`
	Metadata            interface{}   `json:"metadata,omitempty" url:"metadata,omitempty"`
	SourceTraceId       *string       `json:"sourceTraceId,omitempty" url:"sourceTraceId,omitempty"`
	SourceObservationId *string       `json:"sourceObservationId,omitempty" url:"sourceObservationId,omitempty"`
	DatasetId           string        `json:"datasetId" url:"datasetId"`
	DatasetName         string        `json:"datasetName" url:"datasetName"`
	CreatedAt           time.Time     `json:"createdAt" url:"createdAt"`
	UpdatedAt           time.Time     `json:"updatedAt" url:"updatedAt"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (d *DatasetItem) GetId() string {
	if d == nil {
		return ""
	}
	return d.Id
}

func (d *DatasetItem) GetStatus() DatasetStatus {
	if d == nil {
		return ""
	}
	return d.Status
}

func (d *DatasetItem) GetInput() interface{} {
	if d == nil {
		return nil
	}
	return d.Input
}

func (d *DatasetItem) GetExpectedOutput() interface{} {
	if d == nil {
		return nil
	}
	return d.ExpectedOutput
}

func (d *DatasetItem) GetMetadata() interface{} {
	if d == nil {
		return nil
	}
	return d.Metadata
}

func (d *DatasetItem) GetSourceTraceId() *string {
	if d == nil {
		return nil
	}
	return d.SourceTraceId
}

func (d *DatasetItem) GetSourceObservationId() *string {
	if d == nil {
		return nil
	}
	return d.SourceObservationId
}

func (d *DatasetItem) GetDatasetId() string {
	if d == nil {
		return ""
	}
	return d.DatasetId
}

func (d *DatasetItem) GetDatasetName() string {
	if d == nil {
		return ""
	}
	return d.DatasetName
}

func (d *DatasetItem) GetCreatedAt() time.Time {
	if d == nil {
		return time.Time{}
	}
	return d.CreatedAt
}

func (d *DatasetItem) GetUpdatedAt() time.Time {
	if d == nil {
		return time.Time{}
	}
	return d.UpdatedAt
}

func (d *DatasetItem) GetExtraProperties() map[string]interface{} {
	return d.extraProperties
}

func (d *DatasetItem) UnmarshalJSON(data []byte) error {
	type embed DatasetItem
	var unmarshaler = struct {
		embed
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed: embed(*d),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*d = DatasetItem(unmarshaler.embed)
	d.CreatedAt = unmarshaler.CreatedAt.Time()
	d.UpdatedAt = unmarshaler.UpdatedAt.Time()
	extraProperties, err := internal.ExtractExtraProperties(data, *d)
	if err != nil {
		return err
	}
	d.extraProperties = extraProperties
	d.rawJSON = json.RawMessage(data)
	return nil
}

func (d *DatasetItem) MarshalJSON() ([]byte, error) {
	type embed DatasetItem
	var marshaler = struct {
		embed
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed:     embed(*d),
		CreatedAt: internal.NewDateTime(d.CreatedAt),
		UpdatedAt: internal.NewDateTime(d.UpdatedAt),
	}
	return json.Marshal(marshaler)
}

func (d *DatasetItem) String() string {
	if len(d.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(d.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(d); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", d)
}

type DatasetStatus string

const (
	DatasetStatusActive   DatasetStatus = "ACTIVE"
	DatasetStatusArchived DatasetStatus = "ARCHIVED"
)

func NewDatasetStatusFromString(s string) (DatasetStatus, error) {
	switch s {
	case "ACTIVE":
		return DatasetStatusActive, nil
	case "ARCHIVED":
		return DatasetStatusArchived, nil
	}
	var t DatasetStatus
	return "", fmt.Errorf("%s is not a valid %T", s, t)
}

func (d DatasetStatus) Ptr() *DatasetStatus {
	return &d
}

type PaginatedDatasetItems struct {
	Data []*DatasetItem     `json:"data,omitempty" url:"data,omitempty"`
	Meta *UtilsMetaResponse `json:"meta,omitempty" url:"meta,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (p *PaginatedDatasetItems) GetData() []*DatasetItem {
	if p == nil {
		return nil
	}
	return p.Data
}

func (p *PaginatedDatasetItems) GetMeta() *UtilsMetaResponse {
	if p == nil {
		return nil
	}
	return p.Meta
}

func (p *PaginatedDatasetItems) GetExtraProperties() map[string]interface{} {
	return p.extraProperties
}

func (p *PaginatedDatasetItems) UnmarshalJSON(data []byte) error {
	type unmarshaler PaginatedDatasetItems
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*p = PaginatedDatasetItems(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *p)
	if err != nil {
		return err
	}
	p.extraProperties = extraProperties
	p.rawJSON = json.RawMessage(data)
	return nil
}

func (p *PaginatedDatasetItems) String() string {
	if len(p.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(p.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(p); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", p)
}
