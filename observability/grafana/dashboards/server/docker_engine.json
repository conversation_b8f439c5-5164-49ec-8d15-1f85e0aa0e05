{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Draw docker engine metrics", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 11, "links": [], "panels": [{"description": "", "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 3, "w": 24, "x": 0, "y": 0}, "id": 2, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "markdown"}, "pluginVersion": "9.2.8", "repeat": "instance", "repeatDirection": "h", "title": "$instance", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "description": "Number of CPUs", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 24, "x": 0, "y": 3}, "id": 7, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.8", "repeat": "instance", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "engine_daemon_engine_cpus_cpus{instance=~'$instance'}", "intervalFactor": 2, "legendFormat": "", "metric": "engine_daemon_engine_cpus_cpus", "refId": "A", "step": 60}], "title": "CPU Cores", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "description": "Measuring some percentiles performance", "fieldConfig": {"defaults": {}, "overrides": [{"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsZero", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}, {"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsNull", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}]}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 6}, "id": 14, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.2.8", "repeat": "instance", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "histogram_quantile(0.99, rate(engine_daemon_container_actions_seconds_bucket{instance=~'$instance'}[$interval]))", "intervalFactor": 2, "legendFormat": "{{action}} 99", "refId": "A", "step": 4}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "histogram_quantile(0.90, rate(engine_daemon_container_actions_seconds_bucket{instance=~'$instance'}[$interval]))", "intervalFactor": 2, "legendFormat": "{{action}} 90", "refId": "B", "step": 4}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "histogram_quantile(0.50, rate(engine_daemon_container_actions_seconds_bucket{instance=~'$instance'}[$interval]))", "intervalFactor": 2, "legendFormat": "{{action}} 50", "refId": "C", "step": 4}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "histogram_quantile(0.25, rate(engine_daemon_container_actions_seconds_bucket{instance=~'$instance'}[$interval]))", "intervalFactor": 2, "legendFormat": "{{action}} 25", "refId": "D", "step": 4}], "title": "Time x Container Action percentile", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "fieldConfig": {"defaults": {}, "overrides": [{"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsZero", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}, {"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsNull", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}]}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 13}, "id": 15, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.2.8", "repeat": "instance", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "engine_daemon_container_actions_seconds_count{instance=~'$instance'}", "intervalFactor": 2, "legendFormat": "{{action}}", "metric": "engine_daemon_container_actions_seconds_count", "refId": "A", "step": 4}], "title": "Total Container Actions", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "description": "Measuring some percentiles performance", "fieldConfig": {"defaults": {}, "overrides": [{"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsZero", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}, {"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsNull", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}]}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 20}, "id": 22, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.2.8", "repeat": "instance", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "histogram_quantile(0.99, rate(engine_daemon_network_actions_seconds_bucket{instance=~'$instance'}[$interval]))", "intervalFactor": 2, "legendFormat": "{{action}} 99", "refId": "A", "step": 4}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "histogram_quantile(0.90, rate(engine_daemon_network_actions_seconds_bucket{instance=~'$instance'}[$interval]))", "intervalFactor": 2, "legendFormat": "{{action}} 90", "refId": "B", "step": 4}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "histogram_quantile(0.50, rate(engine_daemon_network_actions_seconds_bucket{instance=~'$instance'}[$interval]))", "intervalFactor": 2, "legendFormat": "{{action}} 50", "refId": "C", "step": 4}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "histogram_quantile(0.25, rate(engine_daemon_network_actions_seconds_bucket{instance=~'$instance'}[$interval]))", "intervalFactor": 2, "legendFormat": "{{action}} 25", "refId": "D", "step": 4}], "title": "Time x Network Action percentile", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "fieldConfig": {"defaults": {}, "overrides": [{"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsZero", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}, {"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsNull", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}]}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 27}, "id": 19, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.2.8", "repeat": "instance", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "engine_daemon_network_actions_seconds_count{instance=~'$instance'}", "intervalFactor": 2, "legendFormat": "{{action}}", "metric": "engine_daemon_container_actions_seconds_count", "refId": "A", "step": 4}], "title": "Total Network Actions", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 34}, "id": 20, "options": {"alertThreshold": true}, "pluginVersion": "9.2.8", "repeat": "instance", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "engine_daemon_events_subscribers_total{instance=~'$instance'}", "intervalFactor": 2, "legendFormat": " ", "refId": "A", "step": 4}], "title": "Event Subscribers", "type": "timeseries"}], "preload": false, "schemaVersion": 40, "tags": ["docker", "docker metrics", "docker engine"], "templating": {"list": [{"current": {"text": "VictoriaMetrics", "value": "victoriametrics"}, "description": "", "label": "datasource", "name": "DS_PROMETHEUS", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "type": "datasource"}, {"auto": true, "auto_count": 30, "auto_min": "10s", "current": {"text": "$__auto", "value": "$__auto"}, "label": "Interval", "name": "interval", "options": [{"selected": false, "text": "30s", "value": "30s"}, {"selected": false, "text": "1m", "value": "1m"}, {"selected": false, "text": "2m", "value": "2m"}, {"selected": false, "text": "3m", "value": "3m"}, {"selected": false, "text": "5m", "value": "5m"}, {"selected": false, "text": "7m", "value": "7m"}, {"selected": false, "text": "10m", "value": "10m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "12h", "value": "12h"}, {"selected": false, "text": "1d", "value": "1d"}, {"selected": false, "text": "7d", "value": "7d"}, {"selected": false, "text": "14d", "value": "14d"}, {"selected": false, "text": "30d", "value": "30d"}], "query": "30s,1m,2m,3m,5m,7m,10m,30m,1h,6h,12h,1d,7d,14d,30d", "refresh": 2, "type": "interval"}, {"current": {"text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "victoriametrics"}, "definition": "", "includeAll": true, "label": "Instance", "multi": true, "name": "instance", "options": [], "query": {"query": "engine_daemon_engine_info", "refId": "VictoriaMetrics-instance-Variable-Query"}, "refresh": 1, "regex": "/instance=\"([^\"]+)\"/", "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "Docker Engine", "uid": "de875l5ywwiyof", "version": 6, "weekStart": ""}