@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 222.2 84% 4.9%;
        --card: 0 0% 100%;
        --card-foreground: 222.2 84% 4.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 222.2 84% 4.9%;
        --primary: 222.2 47.4% 11.2%;
        --primary-foreground: 210 40% 98%;
        --secondary: 210 40% 96.1%;
        --secondary-foreground: 222.2 47.4% 11.2%;
        --muted: 210 40% 96.1%;
        --muted-foreground: 215.4 16.3% 46.9%;
        --accent: 210 40% 96.1%;
        --accent-foreground: 222.2 47.4% 11.2%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 210 40% 98%;
        --border: 214.3 31.8% 91.4%;
        --input: 214.3 31.8% 91.4%;
        --ring: 222.2 84% 4.9%;
        --radius: 0.5rem;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --sidebar-background: 0 0% 98%;
        --sidebar-foreground: 240 5.3% 26.1%;
        --sidebar-primary: 240 5.9% 10%;
        --sidebar-primary-foreground: 0 0% 98%;
        --sidebar-accent: 240 4.8% 95.9%;
        --sidebar-accent-foreground: 240 5.9% 10%;
        --sidebar-border: 220 13% 91%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }

    .dark {
        --background: 222.2 84% 4.9%;
        --foreground: 210 40% 98%;
        --card: 222.2 84% 4.9%;
        --card-foreground: 210 40% 98%;
        --popover: 222.2 84% 4.9%;
        --popover-foreground: 210 40% 98%;
        --primary: 210 40% 98%;
        --primary-foreground: 222.2 47.4% 11.2%;
        --secondary: 217.2 32.6% 17.5%;
        --secondary-foreground: 210 40% 98%;
        --muted: 217.2 32.6% 17.5%;
        --muted-foreground: 215 20.2% 65.1%;
        --accent: 217.2 32.6% 17.5%;
        --accent-foreground: 210 40% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 210 40% 98%;
        --border: 217.2 32.6% 17.5%;
        --input: 217.2 32.6% 17.5%;
        --ring: 212.7 26.8% 83.9%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
        --sidebar-background: 240 5.9% 10%;
        --sidebar-foreground: 240 4.8% 95.9%;
        --sidebar-primary: 224.3 76.3% 48%;
        --sidebar-primary-foreground: 0 0% 100%;
        --sidebar-accent: 240 3.7% 15.9%;
        --sidebar-accent-foreground: 240 4.8% 95.9%;
        --sidebar-border: 240 3.7% 15.9%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }

    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    ::-webkit-scrollbar-track {
        background: transparent;
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
        background: hsl(var(--muted-foreground) / 0.3);
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: hsl(var(--muted-foreground) / 0.5);
    }

    * {
        scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
    }
}

@layer base {
    * {
        @apply border-border;
    }
    body {
        @apply bg-background text-foreground;
    }
}

.prose-xs {
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.prose-xs hr {
    margin-top: 1.5em;
    margin-bottom: 1.5em;
}

.prose-xs h1,
.prose-xs h2,
.prose-xs h3,
.prose-xs h4,
.prose-xs h5,
.prose-xs h6 {
    margin-top: 1.5em;
    margin-bottom: 0.5em;
}

.prose-xs p {
    margin-top: 1.5em;
    margin-bottom: 1.5em;
}

.prose-xs p:first-child {
    margin-top: 0;
}

.prose-xs p:last-child {
    margin-bottom: 0;
}

.prose-xs pre {
    margin-top: 1.5em;
    font-size: 1em;
    line-height: 1.5;
}

.prose-xs ul,
.prose-xs ol {
    margin-top: 1.5em;
    margin-bottom: 1.5em;
}

.prose-xs ul ul,
.prose-xs ul ol,
.prose-xs ol ol,
.prose-xs ol ul {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
}

.prose-xs li {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
}

.prose-xs ul > li > p,
.prose-xs ol > li > p,
.prose-xs ul > li > pre,
.prose-xs ol > li > pre {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
}

.prose-xs ul > li > p:first-child,
.prose-xs ol > li > p:first-child {
    margin-top: 0;
}

.prose-xs ul > li > p:last-child,
.prose-xs ol > li > p:last-child {
    margin-bottom: 0;
}

.prose-xs h1 + ul,
.prose-xs h1 + ol,
.prose-xs h2 + ul,
.prose-xs h2 + ol,
.prose-xs h3 + ul,
.prose-xs h3 + ol,
.prose-xs h4 + ul,
.prose-xs h4 + ol,
.prose-xs h5 + ul,
.prose-xs h5 + ol,
.prose-xs h6 + ul,
.prose-xs h6 ol {
    margin-top: 0;
}

.prose-xs :first-child {
    margin-top: 0;
}

.prose-xs :last-child {
    margin-bottom: 0;
}

.prose-fixed h1,
.prose-fixed h2,
.prose-fixed h3,
.prose-fixed h4,
.prose-fixed h5,
.prose-fixed h6 {
    font-size: 1em;
    line-height: 1.5;
}

.prose pre code.hljs {
    color: inherit;
    background: inherit;
}

.prose pre code.hljs {
    padding: 0;
    overflow: visible;
}
