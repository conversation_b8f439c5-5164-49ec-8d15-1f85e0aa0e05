fragment flowOverviewFragment on Flow {
    id
    title
    status
}

query flows {
    flows {
        ...flowOverviewFragment
    }
}

query providers {
    providers
}

fragment settingsFragment on Settings {
    debug
    askUser
    dockerInside
    assistantUseAgents
}

query settings {
    settings {
        ...settingsFragment
    }
}

fragment flowFragment on Flow {
    id
    title
    status
    terminals {
        ...terminalFragment
    }
    provider
    createdAt
    updatedAt
}

fragment terminalFragment on Terminal {
    id
    type
    name
    image
    connected
    createdAt
}

fragment taskFragment on Task {
    id
    title
    status
    input
    result
    flowId
    subtasks {
        ...subtaskFragment
    }
    createdAt
    updatedAt
}

fragment subtaskFragment on Subtask {
    id
    status
    title
    description
    result
    taskId
    createdAt
    updatedAt
}

fragment terminalLogFragment on TerminalLog {
    id
    flowId
    type
    text
    terminal
    createdAt
}

fragment messageLogFragment on MessageLog {
    id
    type
    message
    thinking
    result
    resultFormat
    flowId
    taskId
    subtaskId
    createdAt
}

fragment screenshotFragment on Screenshot {
    id
    flowId
    name
    url
    createdAt
}

fragment agentLogFragment on AgentLog {
    id
    flowId
    initiator
    executor
    task
    result
    taskId
    subtaskId
    createdAt
}

fragment searchLogFragment on SearchLog {
    id
    flowId
    initiator
    executor
    engine
    query
    result
    taskId
    subtaskId
    createdAt
}

fragment vectorStoreLogFragment on VectorStoreLog {
    id
    flowId
    initiator
    executor
    filter
    query
    action
    result
    taskId
    subtaskId
    createdAt
}

fragment assistantFragment on Assistant {
    id
    title
    status
    provider
    flowId
    useAgents
    createdAt
    updatedAt
}

fragment assistantLogFragment on AssistantLog {
    id
    type
    message
    thinking
    result
    resultFormat
    appendPart
    flowId
    assistantId
    createdAt
}

fragment promptFragment on Prompt {
    type
    prompt
}

query flow($id: ID!) {
    flow(flowId: $id) {
        ...flowFragment
    }
    tasks(flowId: $id) {
        ...taskFragment
    }
    screenshots(flowId: $id) {
        ...screenshotFragment
    }
    terminalLogs(flowId: $id) {
        ...terminalLogFragment
    }
    messageLogs(flowId: $id) {
        ...messageLogFragment
    }
    agentLogs(flowId: $id) {
        ...agentLogFragment
    }
    searchLogs(flowId: $id) {
        ...searchLogFragment
    }
    vectorStoreLogs(flowId: $id) {
        ...vectorStoreLogFragment
    }
}

query tasks($flowId: ID!) {
    tasks(flowId: $flowId) {
        ...taskFragment
    }
}

query prompts {
    prompts {
        ...promptFragment
    }
}

query prompt($promptType: String!) {
    prompt(promptType: $promptType)
}

query assistants($flowId: ID!) {
    assistants(flowId: $flowId) {
        ...assistantFragment
    }
}

query assistantLogs($flowId: ID!, $assistantId: ID!) {
    assistantLogs(flowId: $flowId, assistantId: $assistantId) {
        ...assistantLogFragment
    }
}

query flowReport($id: ID!) {
    flow(flowId: $id) {
        ...flowFragment
    }
    tasks(flowId: $id) {
        ...taskFragment
    }
}

mutation createFlow($modelProvider: String!, $input: String!) {
    createFlow(modelProvider: $modelProvider, input: $input) {
        ...flowFragment
    }
}

mutation deleteFlow($flowId: ID!) {
    deleteFlow(flowId: $flowId)
}

mutation putUserInput($flowId: ID!, $input: String!) {
    putUserInput(flowId: $flowId, input: $input)
}

mutation finishFlow($flowId: ID!) {
    finishFlow(flowId: $flowId)
}

mutation stopFlow($flowId: ID!) {
    stopFlow(flowId: $flowId)
}

mutation createAssistant($flowId: ID!, $modelProvider: String!, $input: String!, $useAgents: Boolean!) {
    createAssistant(flowId: $flowId, modelProvider: $modelProvider, input: $input, useAgents: $useAgents) {
        flow {
            ...flowFragment
        }
        assistant {
            ...assistantFragment
        }
    }
}

mutation callAssistant($flowId: ID!, $assistantId: ID!, $input: String!, $useAgents: Boolean!) {
    callAssistant(flowId: $flowId, assistantId: $assistantId, input: $input, useAgents: $useAgents)
}

mutation stopAssistant($flowId: ID!, $assistantId: ID!) {
    stopAssistant(flowId: $flowId, assistantId: $assistantId) {
        ...assistantFragment
    }
}

mutation deleteAssistant($flowId: ID!, $assistantId: ID!) {
    deleteAssistant(flowId: $flowId, assistantId: $assistantId)
}

mutation updatePrompt($promptType: String!, $prompt: String!) {
    updatePrompt(promptType: $promptType, prompt: $prompt)
}

mutation resetPrompt($promptType: String!) {
    resetPrompt(promptType: $promptType)
}

subscription terminalLogAdded($flowId: ID!) {
    terminalLogAdded(flowId: $flowId) {
        ...terminalLogFragment
    }
}

subscription messageLogAdded($flowId: ID!) {
    messageLogAdded(flowId: $flowId) {
        ...messageLogFragment
    }
}

subscription messageLogUpdated($flowId: ID!) {
    messageLogUpdated(flowId: $flowId) {
        ...messageLogFragment
    }
}

subscription screenshotAdded($flowId: ID!) {
    screenshotAdded(flowId: $flowId) {
        ...screenshotFragment
    }
}

subscription agentLogAdded($flowId: ID!) {
    agentLogAdded(flowId: $flowId) {
        ...agentLogFragment
    }
}

subscription searchLogAdded($flowId: ID!) {
    searchLogAdded(flowId: $flowId) {
        ...searchLogFragment
    }
}

subscription vectorStoreLogAdded($flowId: ID!) {
    vectorStoreLogAdded(flowId: $flowId) {
        ...vectorStoreLogFragment
    }
}

subscription assistantCreated($flowId: ID!) {
    assistantCreated(flowId: $flowId) {
        ...assistantFragment
    }
}

subscription assistantUpdated($flowId: ID!) {
    assistantUpdated(flowId: $flowId) {
        ...assistantFragment
    }
}

subscription assistantDeleted($flowId: ID!) {
    assistantDeleted(flowId: $flowId) {
        ...assistantFragment
    }
}

subscription assistantLogAdded($flowId: ID!) {
    assistantLogAdded(flowId: $flowId) {
        ...assistantLogFragment
    }
}

subscription assistantLogUpdated($flowId: ID!) {
    assistantLogUpdated(flowId: $flowId) {
        ...assistantLogFragment
    }
}

subscription flowCreated {
    flowCreated {
        id
        title
        status
        terminals {
            ...terminalFragment
        }
        provider
        createdAt
        updatedAt
    }
}

subscription flowDeleted {
    flowDeleted {
        id
        status
        updatedAt
    }
}

subscription flowUpdated {
    flowUpdated {
        id
        title
        status
        terminals {
            ...terminalFragment
        }
        updatedAt
    }
}

subscription taskCreated($flowId: ID!) {
    taskCreated(flowId: $flowId) {
        ...taskFragment
    }
}

subscription taskUpdated($flowId: ID!) {
    taskUpdated(flowId: $flowId) {
        id
        status
        result
        subtasks {
            ...subtaskFragment
        }
        updatedAt
    }
}
