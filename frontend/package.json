{"name": "pentagi", "type": "module", "version": "0.2.0", "scripts": {"build": "npx tsc && vite build", "commit": "commit", "commitlint": "commitlint --edit", "dev": "vite", "graphql:generate": "graphql-codegen --config graphql-codegen.ts", "lint": "eslint \"src/**/*.{ts,tsx,js,jsx}\"", "lint:fix": "eslint \"src/**/*.{ts,tsx,js,jsx}\" --fix", "prettier": "npx prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,scss}\"", "prettier:fix": "npx prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss}\"", "ssl:generate": "tsx --eval 'import { generateCertificates } from \"./scripts/generate-ssl.ts\"; generateCertificates();'", "test": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest"}, "dependencies": {"@apollo/client": "^3.13.8", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.3", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-search": "^0.15.0", "@xterm/addon-unicode11": "^0.8.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/addon-webgl": "^0.18.0", "@xterm/xterm": "^5.5.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "graphql": "^16.11.0", "graphql-ws": "^6.0.5", "html2pdf.js": "^0.10.3", "js-cookie": "^3.0.5", "lru-cache": "^11.1.0", "lucide-react": "^0.454.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.4", "react-markdown": "^9.1.0", "react-query": "^3.39.3", "react-resizable-panels": "^3.0.2", "react-router-dom": "^7.6.1", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "rehype-slug": "^6.0.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.32", "zustand": "^5.0.5"}, "devDependencies": {"@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@graphql-codegen/cli": "^5.0.3", "@graphql-codegen/client-preset": "^4.5.1", "@graphql-codegen/near-operation-file-preset": "^3.0.0", "@graphql-codegen/typescript": "^4.1.1", "@graphql-codegen/typescript-operations": "^4.3.1", "@graphql-codegen/typescript-react-apollo": "^4.3.2", "@prettier/plugin-xml": "^3.3.1", "@tailwindcss/typography": "^0.5.15", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.13", "@types/node": "^20.16.6", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react-swc": "^3.8.0", "@vitest/coverage-v8": "^3.0.7", "autoprefixer": "^10.4.20", "eslint": "^9.11.1", "eslint-config-hyoban": "^3.2.1", "lint-staged": "^15.2.10", "postcss": "^8.4.47", "prettier": "^3.3.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.8", "simple-git-hooks": "^2.11.1", "tailwindcss": "^3.4.13", "tsx": "^4.19.3", "typescript": "^5.6.2", "vite": "^6.3.4", "vite-plugin-html": "^3.2.2", "vite-tsconfig-paths": "^5.0.1", "vitest": "^3.0.7"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}