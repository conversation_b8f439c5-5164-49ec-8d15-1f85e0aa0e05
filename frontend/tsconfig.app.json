{
    "compilerOptions": {
        /* Base Options: */
        "esModuleInterop": true,
        "skipLibCheck": true,
        "target": "es2022",
        "allowJs": true,
        "resolveJsonModule": true,
        "moduleDetection": "force",
        "isolatedModules": true,
        "verbatimModuleSyntax": true,

        /* Strictness */
        "strict": true,
        "noUncheckedIndexedAccess": true,
        "noImplicitOverride": true,

        /* If NOT transpiling with TypeScript: */
        "module": "preserve",
        "noEmit": true,

        /* If your code runs in the DOM: */
        "lib": ["es2022", "dom", "dom.iterable"],
        "jsx": "react-jsx",

        "baseUrl": ".",
        "paths": {
            "@/*": ["./src/*"],
            "@env": ["./env.ts"],
            "@pkg": ["./package.json"],

            "@/ui/*": ["./src/components/ui/*"]
        }
    },
    "include": ["src", "./env.ts", "types/**/*.d.ts"]
}
